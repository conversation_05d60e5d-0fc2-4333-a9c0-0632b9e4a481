# Ventusky Weather System 架构文档

## 🏗️ 系统架构概览

```
Ventusky Weather System
├── 🐍 Python脚本系统
│   ├── 自动化数据获取
│   ├── 批量处理能力
│   └── 专业数据导出
└── 🌐 Chrome插件系统
    ├── 实时交互获取
    ├── 即开即用体验
    └── 轻量级部署
```

## 📁 项目结构

```
VentuskyWeatherSystem/
├── 📄 README.md                           # 项目主文档
├── 📄 ARCHITECTURE.md                     # 架构文档
├── 📄 requirements.txt                    # Python依赖
├── 📄 main.py                            # 主启动脚本
├── 📄 ventusky_complete_weather_excel.py  # 完整天气系统
├── 📄 ventusky_multi_day_forecast_fixed.py # 多日预报系统
├── 📄 VentuskyWeatherSystem.pyproj       # Visual Studio项目文件
│
├── 📁 chrome-extension/                   # Chrome插件系统
│   ├── 📄 manifest.json                  # 插件配置
│   ├── 📄 content-fixed-v2.js           # 修复版内容脚本 ⭐
│   ├── 📄 content-debug.js              # 调试版本
│   ├── 📄 popup.html/js                 # 插件界面
│   ├── 📄 background.js                 # 后台服务
│   ├── 📄 options.html/js               # 设置页面
│   ├── 📁 icons/                        # 插件图标
│   └── 📁 docs/                         # 插件文档
│       ├── 📄 QUICK_START.md            # 快速开始
│       ├── 📄 COMPLETE_FIX_GUIDE.md     # 完整修复指南
│       ├── 📄 INSTALLATION_GUIDE.md     # 安装指南
│       └── 📄 TIMELINE_FIX_README.md    # 技术文档
│
├── 📁 legacy/                            # 历史版本
│   ├── 📄 ventusky_july26_*.py          # 专项功能脚本
│   └── 📄 ventusky_final_success_scraper.py
│
├── 📁 output/                            # 输出数据
│   ├── 📄 *.json                        # JSON格式数据
│   ├── 📄 *.xlsx                        # Excel报表
│   └── 📄 *.csv                         # CSV数据
│
├── 📁 data/                              # 数据目录
└── 📁 scripts/                           # 辅助脚本
```

## 🔧 技术架构

### Python脚本系统架构

```
┌─────────────────────────────────────────┐
│           Python脚本系统                │
├─────────────────────────────────────────┤
│  🎯 主控制器 (main.py)                  │
│  ├── 交互式菜单                        │
│  ├── 功能选择                          │
│  └── 参数配置                          │
├─────────────────────────────────────────┤
│  📊 数据获取引擎                        │
│  ├── ventusky_complete_weather_excel.py │
│  ├── ventusky_multi_day_forecast_fixed.py │
│  └── 时间轴切换算法                    │
├─────────────────────────────────────────┤
│  🔄 数据处理层                          │
│  ├── 数据提取算法                      │
│  ├── 格式标准化                        │
│  └── 质量验证                          │
├─────────────────────────────────────────┤
│  💾 数据输出层                          │
│  ├── JSON格式                          │
│  ├── Excel报表                         │
│  └── CSV数据                           │
└─────────────────────────────────────────┘
```

### Chrome插件系统架构

```
┌─────────────────────────────────────────┐
│          Chrome插件系统                 │
├─────────────────────────────────────────┤
│  🎨 用户界面层                          │
│  ├── popup.html (弹出界面)              │
│  ├── options.html (设置页面)            │
│  └── 进度显示组件                      │
├─────────────────────────────────────────┤
│  🧠 业务逻辑层                          │
│  ├── content-fixed-v2.js (核心脚本)     │
│  ├── 时间轴查找算法                    │
│  ├── 数据类型切换                      │
│  └── 数据提取引擎                      │
├─────────────────────────────────────────┤
│  🔧 服务支持层                          │
│  ├── background.js (后台服务)           │
│  ├── 消息通信机制                      │
│  └── 存储管理                          │
├─────────────────────────────────────────┤
│  🐛 调试支持层                          │
│  ├── content-debug.js (调试版本)        │
│  ├── 详细日志输出                      │
│  └── 测试工具函数                      │
└─────────────────────────────────────────┘
```

## 🔄 数据流程

### 1. Python脚本数据流程

```
用户输入参数 → 启动Selenium → 导航到Ventusky → 
设置位置/日期 → 查找时间轴元素 → 循环6个时间点 → 
切换数据类型 → 提取数据 → 格式化处理 → 
生成报表 → 保存文件
```

### 2. Chrome插件数据流程

```
用户点击插件 → 获取当前坐标 → 注入内容脚本 → 
查找时间轴元素 → 循环6个时间点 → 切换数据类型 → 
提取数据 → 实时显示进度 → 整合数据 → 
显示结果 → 提供下载
```

## 🧮 核心算法

### 时间轴切换算法

```python
# Python版本
def calculate_timeline_index(target_hour, total_elements):
    hours_per_element = 24 / total_elements
    element_index = int(target_hour / hours_per_element)
    return max(0, min(element_index, total_elements - 1))
```

```javascript
// JavaScript版本
function calculateTimelineIndex(targetHour, totalElements) {
    const hoursPerElement = 24 / totalElements;
    let elementIndex = Math.floor(targetHour / hoursPerElement);
    return Math.max(0, Math.min(elementIndex, totalElements - 1));
}
```

### 多级筛选算法

```javascript
const filterConditions = [
    // 严格条件 → 宽松条件
    { filter: (item) => strictCondition(item) },
    { filter: (item) => moderateCondition(item) },
    { filter: (item) => looseCondition(item) }
];

// 智能选择最佳筛选结果
for (const condition of filterConditions) {
    const filtered = elements.filter(condition.filter);
    if (filtered.length >= 6 && filtered.length <= 24) {
        return filtered; // 找到合适的元素数量
    }
}
```

## 🔒 安全性设计

### 权限控制
- **最小权限原则**: 只请求必要的浏览器权限
- **域名限制**: 仅在www.ventusky.com域名下运行
- **数据隔离**: 不收集用户个人信息

### 错误处理
- **优雅降级**: 多种尝试策略确保成功率
- **异常捕获**: 完整的try-catch错误处理
- **状态恢复**: 失败后自动重试机制

## 📈 性能优化

### Python脚本优化
- **智能等待**: 动态调整等待时间
- **资源复用**: 单个浏览器实例处理多个任务
- **内存管理**: 及时释放不需要的对象

### Chrome插件优化
- **异步处理**: 使用Promise避免阻塞
- **事件驱动**: 基于消息传递的架构
- **缓存策略**: 避免重复查找DOM元素

## 🔮 扩展性设计

### 新数据类型支持
- **模块化设计**: 易于添加新的数据提取器
- **统一接口**: 标准化的数据提取API
- **配置驱动**: 通过配置文件添加新功能

### 多网站支持
- **抽象层设计**: 核心逻辑与网站特定代码分离
- **适配器模式**: 为不同网站创建适配器
- **插件架构**: 支持第三方扩展

## 🎯 设计原则

1. **一致性**: Python脚本和Chrome插件使用相同的核心算法
2. **可靠性**: 多重错误处理和恢复机制
3. **易用性**: 简单直观的用户界面
4. **可维护性**: 清晰的代码结构和完整的文档
5. **可扩展性**: 模块化设计支持功能扩展

---

**这个架构确保了系统的稳定性、可靠性和可扩展性，为用户提供了专业级的天气数据获取解决方案。** 🏗️
