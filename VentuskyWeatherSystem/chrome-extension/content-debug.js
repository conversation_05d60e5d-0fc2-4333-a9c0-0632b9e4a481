// Ventusky Weather Extractor - Debug版本
// 用于调试时间轴元素查找和风速切换问题

class VentuskyWeatherExtractorDebug {
    constructor() {
        this.isExtracting = false;
        this.targetHours = [2, 5, 8, 11, 17, 23];
        
        this.setupMessageListener();
        console.log('🐛 Ventusky Weather Extractor Debug版本已加载');
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('📨 Debug版本收到消息:', message);
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'debugTimelineElements':
                    const result = await this.debugTimelineElements();
                    sendResponse(result);
                    break;
                    
                case 'debugWindspeedSwitch':
                    const windResult = await this.debugWindspeedSwitch();
                    sendResponse(windResult);
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown debug action' });
            }
        } catch (error) {
            console.error('❌ Debug error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async debugTimelineElements() {
        console.log('🐛 开始调试时间轴元素查找...');
        
        const allLinks = document.querySelectorAll('a');
        console.log(`🔍 页面总共有${allLinks.length}个<a>标签`);
        
        const candidates = [];
        const bottomElements = [];
        
        for (let i = 0; i < allLinks.length; i++) {
            const link = allLinks[i];
            
            try {
                if (link.offsetParent !== null) { // 元素可见
                    const rect = link.getBoundingClientRect();
                    const location = { x: rect.left, y: rect.top };
                    const size = { width: rect.width, height: rect.height };
                    const text = link.textContent || '';
                    const href = link.href || '';
                    const className = link.className || '';
                    
                    // 收集页面底部的元素（y > 700）
                    if (location.y > 700) {
                        bottomElements.push({
                            index: i,
                            text: text.trim(),
                            x: Math.round(location.x),
                            y: Math.round(location.y),
                            width: Math.round(size.width),
                            height: Math.round(size.height),
                            href: href,
                            className: className
                        });
                    }
                    
                    // 使用不同的筛选条件测试
                    const conditions = [
                        // 原始条件
                        {
                            name: '原始条件(y>800, w:30-80, h:20-50)',
                            test: location.y > 800 && size.width > 30 && size.width < 80 && size.height > 20 && size.height < 50
                        },
                        // 放宽Y坐标条件
                        {
                            name: '放宽Y坐标(y>700, w:30-80, h:20-50)',
                            test: location.y > 700 && size.width > 30 && size.width < 80 && size.height > 20 && size.height < 50
                        },
                        // 放宽尺寸条件
                        {
                            name: '放宽尺寸(y>800, w:20-100, h:15-60)',
                            test: location.y > 800 && size.width > 20 && size.width < 100 && size.height > 15 && size.height < 60
                        },
                        // 最宽松条件
                        {
                            name: '最宽松(y>700, w:20-100, h:15-60)',
                            test: location.y > 700 && size.width > 20 && size.width < 100 && size.height > 15 && size.height < 60
                        },
                        // 时间格式条件
                        {
                            name: '时间格式(包含XX:XX)',
                            test: /^\d{1,2}:\d{2}$/.test(text.trim())
                        }
                    ];
                    
                    for (const condition of conditions) {
                        if (condition.test) {
                            candidates.push({
                                condition: condition.name,
                                index: i,
                                text: text.trim(),
                                x: Math.round(location.x),
                                y: Math.round(location.y),
                                width: Math.round(size.width),
                                height: Math.round(size.height),
                                href: href,
                                className: className
                            });
                        }
                    }
                }
            } catch (error) {
                continue;
            }
        }
        
        // 按Y坐标排序底部元素
        bottomElements.sort((a, b) => b.y - a.y);
        
        console.log('🐛 调试结果:');
        console.log(`📊 页面底部元素(y>700): ${bottomElements.length}个`);
        console.log('📋 底部元素列表(按Y坐标倒序):');
        bottomElements.slice(0, 20).forEach((item, index) => {
            console.log(`  ${index}: "${item.text}" - x:${item.x}, y:${item.y}, w:${item.width}, h:${item.height}, class:"${item.className}"`);
        });
        
        console.log(`🎯 候选时间轴元素: ${candidates.length}个`);
        
        // 按条件分组
        const groupedCandidates = {};
        candidates.forEach(candidate => {
            if (!groupedCandidates[candidate.condition]) {
                groupedCandidates[candidate.condition] = [];
            }
            groupedCandidates[candidate.condition].push(candidate);
        });
        
        for (const [condition, items] of Object.entries(groupedCandidates)) {
            console.log(`📋 ${condition}: ${items.length}个`);
            items.slice(0, 10).forEach((item, index) => {
                console.log(`  ${index}: "${item.text}" - x:${item.x}, y:${item.y}, w:${item.width}, h:${item.height}`);
            });
        }
        
        return {
            success: true,
            totalLinks: allLinks.length,
            bottomElements: bottomElements.slice(0, 20),
            candidates: groupedCandidates
        };
    }

    async debugWindspeedSwitch() {
        console.log('🐛 开始调试风速切换...');
        
        try {
            // 1. 查找风速选项
            console.log('🔍 查找风速选项...');
            const windElements = document.querySelectorAll('*');
            const windOptions = [];
            
            for (const element of windElements) {
                const text = element.textContent || '';
                if ((text.includes('风') || text.includes('Wind') || text.includes('wind')) && 
                    element.offsetParent !== null) {
                    
                    windOptions.push({
                        text: text.trim(),
                        tagName: element.tagName,
                        className: element.className,
                        id: element.id
                    });
                }
            }
            
            console.log(`🎯 找到${windOptions.length}个风速相关元素:`);
            windOptions.slice(0, 10).forEach((item, index) => {
                console.log(`  ${index}: "${item.text}" - ${item.tagName}.${item.className}`);
            });
            
            // 2. 尝试点击风速选项
            let windClicked = false;
            for (const element of windElements) {
                const text = element.textContent || '';
                if ((text.includes('风速') || text === 'Wind') && 
                    element.offsetParent !== null) {
                    
                    console.log(`🎯 尝试点击风速选项: "${text}"`);
                    
                    try {
                        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        await this.sleep(1000);
                        
                        element.click();
                        await this.sleep(3000);
                        
                        console.log('✅ 风速选项点击成功');
                        windClicked = true;
                        break;
                    } catch (error) {
                        console.log(`❌ 点击失败: ${error.message}`);
                        continue;
                    }
                }
            }
            
            if (!windClicked) {
                console.log('❌ 未能点击风速选项');
                return { success: false, error: '未能点击风速选项' };
            }
            
            // 3. 查找高度选项
            console.log('🔍 查找高度选项...');
            await this.sleep(2000); // 等待菜单展开
            
            const heightElements = document.querySelectorAll('*');
            const heightOptions = [];
            
            for (const element of heightElements) {
                const text = element.textContent || '';
                if ((text.includes('米') || text.includes('m')) && 
                    (text.includes('10') || text.includes('100')) &&
                    element.offsetParent !== null) {
                    
                    heightOptions.push({
                        text: text.trim(),
                        tagName: element.tagName,
                        className: element.className,
                        id: element.id
                    });
                }
            }
            
            console.log(`🎯 找到${heightOptions.length}个高度相关元素:`);
            heightOptions.forEach((item, index) => {
                console.log(`  ${index}: "${item.text}" - ${item.tagName}.${item.className}`);
            });
            
            // 4. 尝试切换到100米
            let height100mSelected = false;
            
            // 首先点击10米下拉框
            for (const element of heightElements) {
                const text = element.textContent || '';
                if (text.includes('地上10米') && element.offsetParent !== null) {
                    console.log(`🎯 尝试点击10米下拉框: "${text}"`);
                    
                    try {
                        element.click();
                        await this.sleep(2000);
                        console.log('✅ 10米下拉框点击成功');
                        break;
                    } catch (error) {
                        console.log(`❌ 点击10米下拉框失败: ${error.message}`);
                        continue;
                    }
                }
            }
            
            // 然后选择100米选项
            await this.sleep(1000);
            const updatedElements = document.querySelectorAll('*');
            
            for (const element of updatedElements) {
                const text = element.textContent || '';
                if ((text.includes('地上100米') || text.includes('100米')) && 
                    element.offsetParent !== null) {
                    
                    console.log(`🎯 尝试选择100米选项: "${text}"`);
                    
                    try {
                        element.click();
                        await this.sleep(2000);
                        console.log('✅ 100米选项选择成功');
                        height100mSelected = true;
                        break;
                    } catch (error) {
                        console.log(`❌ 选择100米选项失败: ${error.message}`);
                        continue;
                    }
                }
            }
            
            return {
                success: true,
                windClicked: windClicked,
                height100mSelected: height100mSelected,
                windOptions: windOptions.slice(0, 5),
                heightOptions: heightOptions
            };
            
        } catch (error) {
            console.error('❌ 风速切换调试失败:', error);
            return { success: false, error: error.message };
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 初始化调试版本
const debugExtractor = new VentuskyWeatherExtractorDebug();

// 添加全局调试函数
window.debugVentusky = {
    debugTimelineElements: () => debugExtractor.debugTimelineElements(),
    debugWindspeedSwitch: () => debugExtractor.debugWindspeedSwitch()
};

console.log('💡 调试提示: 可以在控制台调用以下函数:');
console.log('  - window.debugVentusky.debugTimelineElements() // 调试时间轴元素');
console.log('  - window.debugVentusky.debugWindspeedSwitch() // 调试风速切换');
