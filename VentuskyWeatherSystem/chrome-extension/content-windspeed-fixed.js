// Ventusky Weather Extractor - 风速100米修复版
// 精确模拟Python脚本的风速切换逻辑

class VentuskyWindspeedFixer {
    constructor() {
        this.setupMessageListener();
        console.log('💨 Ventusky 风速100米修复器已加载');
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'testWindspeedSwitch') {
                this.testWindspeedSwitch().then(result => {
                    sendResponse(result);
                });
                return true;
            }
        });
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 精确模拟Python脚本的风速切换逻辑
    async switchToWindspeedPrecise() {
        console.log('💨 开始精确风速切换（模拟Python脚本）...');
        
        try {
            // 第一步：点击左侧菜单中的"风速"选项
            const windspeedClicked = await this.clickWindspeedInLeftMenu();
            if (!windspeedClicked) {
                console.log('❌ 风速选项点击失败');
                return false;
            }

            // 第二步：精确点击高度下拉框并选择地上100米
            const height100mSelected = await this.clickHeightDropdownAndSelect100mPrecise();
            
            return {
                windspeedClicked: windspeedClicked,
                height100mSelected: height100mSelected,
                success: windspeedClicked && height100mSelected
            };

        } catch (error) {
            console.error('❌ 精确风速切换失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 精确模拟Python脚本：点击左侧菜单中的"风速"选项
    async clickWindspeedInLeftMenu() {
        console.log('💨 点击左侧菜单中的"风速"选项...');
        
        try {
            // 查找包含"风速"文本的元素
            const windspeedElements = document.querySelectorAll('*');
            
            for (const element of windspeedElements) {
                try {
                    const text = element.textContent || '';
                    if (text.includes('风速') && element.offsetParent !== null) {
                        console.log(`找到风速选项: ${text}`);
                        
                        // 滚动到元素可见
                        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        await this.sleep(1000);
                        
                        // 点击风速选项
                        element.click();
                        
                        console.log('✅ 成功点击左侧菜单中的"风速"选项');
                        await this.sleep(5000); // 等待图层切换
                        
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            
            console.log('❌ 未找到左侧菜单中的风速选项');
            return false;
            
        } catch (error) {
            console.log(`❌ 点击左侧风速选项失败: ${error}`);
            return false;
        }
    }

    // 精确模拟Python脚本：点击高度下拉框并选择地上100米
    async clickHeightDropdownAndSelect100mPrecise() {
        console.log('🏔️ 精确操作：点击"地上10米"下拉框并选择"地上100米"...');
        
        try {
            // 第一步：找到并点击"地上10米"下拉框
            console.log('🔍 查找"地上10米"下拉框...');
            
            let dropdownClicked = false;
            
            // 尝试多种方法找到下拉框（模拟Python的多策略方法）
            const dropdownStrategies = [
                // 策略1：直接查找包含"地上10米"的元素
                () => this.findElementsByText('地上10米'),
                // 策略2：查找包含"10米"的可点击元素
                () => this.findElementsByText('10米'),
                // 策略3：查找高度相关的元素
                () => this.findElementsByText('高度')
            ];
            
            for (let strategyNum = 0; strategyNum < dropdownStrategies.length; strategyNum++) {
                if (dropdownClicked) break;
                
                try {
                    console.log(`尝试策略 ${strategyNum + 1}...`);
                    const elements = dropdownStrategies[strategyNum]();
                    
                    for (const element of elements) {
                        try {
                            if (element.offsetParent !== null) {
                                const text = element.textContent || '';
                                console.log(`找到候选下拉框: "${text}" - 标签: ${element.tagName}`);
                                
                                // 滚动到元素可见
                                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                await this.sleep(1000);
                                
                                // 尝试多种点击方法
                                const clickMethods = [
                                    // 方法1：普通点击
                                    () => element.click(),
                                    // 方法2：鼠标事件
                                    () => {
                                        const event = new MouseEvent('click', {
                                            view: window,
                                            bubbles: true,
                                            cancelable: true
                                        });
                                        element.dispatchEvent(event);
                                    },
                                    // 方法3：JavaScript点击
                                    () => {
                                        if (element.click) element.click();
                                    }
                                ];
                                
                                for (let methodNum = 0; methodNum < clickMethods.length; methodNum++) {
                                    try {
                                        console.log(`  尝试点击方法 ${methodNum + 1}...`);
                                        clickMethods[methodNum]();
                                        await this.sleep(3000); // 等待下拉选项出现
                                        
                                        // 检查是否成功打开下拉框
                                        if (await this.checkDropdownOpened()) {
                                            console.log('✅ 成功打开高度下拉框');
                                            dropdownClicked = true;
                                            break;
                                        }
                                    } catch (error) {
                                        console.log(`    点击方法 ${methodNum + 1} 失败: ${error.message}`);
                                        continue;
                                    }
                                }
                                
                                if (dropdownClicked) break;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                } catch (error) {
                    console.log(`策略 ${strategyNum + 1} 失败: ${error}`);
                    continue;
                }
            }
            
            if (!dropdownClicked) {
                console.log('❌ 未能打开高度下拉框');
                return false;
            }

            // 第二步：在弹出的选择栏中精确选择"地上100米"
            console.log('🔍 在下拉选项中精确查找并点击"地上100米"...');
            
            // 等待下拉选项完全加载
            await this.sleep(2000);
            
            // 查找100米选项的多种策略（模拟Python的多策略方法）
            const optionStrategies = [
                // 策略1：直接查找"地上100米"
                () => this.findElementsByText('地上100米'),
                // 策略2：查找"100米"
                () => this.findElementsByText('100米'),
                // 策略3：查找"100m"
                () => this.findElementsByText('100m'),
                // 策略4：查找包含100的元素
                () => this.findElementsByText('100')
            ];
            
            let optionClicked = false;
            
            for (let strategyNum = 0; strategyNum < optionStrategies.length; strategyNum++) {
                if (optionClicked) break;
                
                try {
                    console.log(`尝试选项策略 ${strategyNum + 1}...`);
                    const elements = optionStrategies[strategyNum]();
                    
                    // 过滤出最可能的100米选项
                    const validElements = [];
                    for (const element of elements) {
                        try {
                            if (element.offsetParent !== null) {
                                const text = element.textContent?.trim() || '';
                                console.log(`  发现选项: "${text}" - 标签: ${element.tagName}`);
                                
                                // 优先选择包含"地上100米"的选项
                                if (text.includes('地上100米')) {
                                    validElements.unshift(element); // 插入到最前面
                                } else if (text.includes('100米') || text.includes('100m')) {
                                    validElements.push(element);
                                }
                            }
                        } catch {
                            continue;
                        }
                    }
                    
                    // 尝试点击最合适的选项
                    for (const element of validElements) {
                        try {
                            const text = element.textContent || '';
                            console.log(`  尝试点击选项: "${text}"`);
                            
                            // 尝试多种点击方法
                            const clickMethods = [
                                () => element.click(),
                                () => {
                                    const event = new MouseEvent('click', {
                                        view: window,
                                        bubbles: true,
                                        cancelable: true
                                    });
                                    element.dispatchEvent(event);
                                }
                            ];
                            
                            for (const clickMethod of clickMethods) {
                                try {
                                    clickMethod();
                                    await this.sleep(3000); // 等待选项应用
                                    
                                    // 验证是否成功选择了100米
                                    if (await this.verify100mSelected()) {
                                        console.log('✅ 成功选择"地上100米"选项');
                                        optionClicked = true;
                                        break;
                                    }
                                } catch (error) {
                                    continue;
                                }
                            }
                            
                            if (optionClicked) break;
                        } catch (error) {
                            continue;
                        }
                    }
                } catch (error) {
                    console.log(`选项策略 ${strategyNum + 1} 失败: ${error}`);
                    continue;
                }
            }
            
            if (optionClicked) {
                return true;
            } else {
                console.log('❌ 未能成功选择"地上100米"选项');
                return false;
            }
            
        } catch (error) {
            console.log(`❌ 精确选择地上100米失败: ${error}`);
            return false;
        }
    }

    // 辅助方法：根据文本查找元素
    findElementsByText(searchText) {
        const elements = [];
        const allElements = document.querySelectorAll('*');
        
        for (const element of allElements) {
            const text = element.textContent || '';
            if (text.includes(searchText)) {
                elements.push(element);
            }
        }
        
        return elements;
    }

    // 检查下拉框是否已打开
    async checkDropdownOpened() {
        try {
            // 查找可能的下拉选项
            const dropdownOptions = this.findElementsByText('100');
            return dropdownOptions.length > 0;
        } catch {
            return false;
        }
    }

    // 验证是否成功选择了100米
    async verify100mSelected() {
        try {
            // 查找页面上是否显示"地上100米"
            const currentSelection = this.findElementsByText('地上100米');
            
            for (const element of currentSelection) {
                if (element.offsetParent !== null) {
                    const text = element.textContent || '';
                    console.log(`当前高度显示: "${text}"`);
                    if (text.includes('100')) {
                        return true;
                    }
                }
            }
            
            // 也检查是否有"100米"显示
            const selection100m = this.findElementsByText('100米');
            for (const element of selection100m) {
                if (element.offsetParent !== null) {
                    const text = element.textContent || '';
                    console.log(`当前高度显示: "${text}"`);
                    if (text.includes('100')) {
                        return true;
                    }
                }
            }
            
            return false;
        } catch {
            return false;
        }
    }

    // 测试风速切换功能
    async testWindspeedSwitch() {
        console.log('🧪 开始测试风速切换功能...');
        
        try {
            const result = await this.switchToWindspeedPrecise();
            
            console.log('🧪 测试结果:', result);
            
            return {
                success: true,
                result: result,
                message: result.success ? '风速切换测试成功' : '风速切换测试失败'
            };
            
        } catch (error) {
            console.error('🧪 测试失败:', error);
            return {
                success: false,
                error: error.message,
                message: '风速切换测试出现错误'
            };
        }
    }
}

// 初始化风速修复器
const windspeedFixer = new VentuskyWindspeedFixer();

// 添加全局测试函数
window.testWindspeedFix = {
    switchToWindspeed: () => windspeedFixer.switchToWindspeedPrecise(),
    testSwitch: () => windspeedFixer.testWindspeedSwitch()
};

console.log('💡 风速修复器已加载，可以在控制台调用:');
console.log('  - window.testWindspeedFix.switchToWindspeed() // 测试风速切换');
console.log('  - window.testWindspeedFix.testSwitch() // 完整测试');
