# Ventusky Chrome插件快速开始指南

## 🚀 5分钟快速上手

### 第一步：安装插件
1. **下载项目**：
   ```bash
   git clone <your-repository>
   cd VentuskyWeatherSystem/chrome-extension/
   ```

2. **安装到Chrome**：
   - 打开Chrome浏览器
   - 地址栏输入：`chrome://extensions/`
   - 开启右上角的"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择 `chrome-extension` 文件夹
   - 看到插件图标出现在工具栏

### 第二步：使用插件
1. **访问Ventusky**：
   ```
   https://www.ventusky.com/
   ```

2. **设置位置**：
   - 在地图上点击目标位置
   - 或使用搜索功能找到城市

3. **启动提取**：
   - 点击浏览器工具栏中的插件图标
   - 在弹出窗口中点击"开始提取"
   - 等待进度条完成

4. **查看结果**：
   - 提取完成后会显示天气数据
   - 可以复制JSON数据或下载文件

## 🔧 如果遇到问题

### 时间轴无法切换？
使用修复版本：
```bash
cd chrome-extension/
cp content-fixed-v2.js content.js
cp manifest-v2.json manifest.json
```
然后在Chrome扩展页面点击"重新加载"。

### 数据获取失败？
1. **检查网络连接**
2. **刷新Ventusky页面**
3. **查看控制台日志**：
   - 按F12打开开发者工具
   - 查看Console标签页的错误信息

### 调试模式
在控制台运行：
```javascript
// 测试时间轴查找
window.ventuskyDebug.findTimelineElements()

// 测试风速切换
window.ventuskyDebug.switchToWindspeed()

// 测试完整流程
window.ventuskyDebug.testExtraction()
```

## 📊 获取的数据格式

```json
{
  "location": {
    "lat": 30.1833,
    "lon": 120.2
  },
  "date": "2025-08-04",
  "data": {
    "02:00": {
      "temperature": "25°C",
      "precipitation": "0mm",
      "cloudcover": "30%",
      "windspeed": "12km/h"
    },
    "05:00": {
      "temperature": "23°C",
      "precipitation": "0.5mm",
      "cloudcover": "45%",
      "windspeed": "8km/h"
    },
    "08:00": {
      "temperature": "28°C",
      "precipitation": "0mm",
      "cloudcover": "20%",
      "windspeed": "15km/h"
    },
    "11:00": {
      "temperature": "32°C",
      "precipitation": "0mm",
      "cloudcover": "10%",
      "windspeed": "18km/h"
    },
    "17:00": {
      "temperature": "35°C",
      "precipitation": "2.3mm",
      "cloudcover": "60%",
      "windspeed": "22km/h"
    },
    "23:00": {
      "temperature": "27°C",
      "precipitation": "1.2mm",
      "cloudcover": "40%",
      "windspeed": "10km/h"
    }
  },
  "extractedAt": "2025-08-04T14:30:00.000Z"
}
```

## 💡 使用技巧

### 1. 最佳使用时机
- 页面完全加载后再启动提取
- 避免在网络不稳定时使用
- 建议在非高峰时段使用

### 2. 数据准确性
- 插件获取的数据与Python脚本完全一致
- 支持全球任意位置的天气数据
- 风速数据为地上100米高度

### 3. 批量获取
- 如需批量获取多个位置或多个日期的数据
- 建议使用Python脚本系统
- Chrome插件更适合单次快速查询

## 🆘 获取帮助

1. **查看完整文档**：
   - `COMPLETE_FIX_GUIDE.md` - 完整修复指南
   - `INSTALLATION_GUIDE.md` - 详细安装指南
   - `TIMELINE_FIX_README.md` - 技术文档

2. **常见问题**：
   - 检查Chrome版本（需要88+）
   - 确保网站权限已授予
   - 尝试刷新页面后重新提取

3. **技术支持**：
   - 提供控制台错误日志
   - 说明使用的浏览器版本
   - 描述具体的问题现象

---

**现在您可以开始使用Ventusky Chrome插件获取精确的天气数据了！** 🌤️
