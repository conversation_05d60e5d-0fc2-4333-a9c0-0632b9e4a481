# Ventusky Chrome插件时间轴修复说明

## 问题描述

原Chrome插件的时间轴切换功能无法按照Python脚本的6个时间点（02:00, 05:00, 08:00, 11:00, 17:00, 23:00）正确切换，导致数据获取不准确。

## 问题根源

### Python脚本的时间轴逻辑（正确）：
```python
def find_timeline_elements(self):
    timeline_elements = []
    all_links = self.driver.find_elements(By.TAG_NAME, "a")
    
    for link in all_links:
        if link.is_displayed():
            location = link.location
            size = link.size
            
            if (location['y'] > 800 and  
                size['width'] > 30 and size['width'] < 80 and  
                size['height'] > 20 and size['height'] < 50):
                
                timeline_elements.append({
                    'element': link,
                    'x_position': location['x']
                })
    
    timeline_elements.sort(key=lambda x: x['x_position'])
    return timeline_elements

def get_data_for_all_hours(self, data_type):
    timeline_elements = self.find_timeline_elements()
    total_elements = len(timeline_elements)
    
    for target_hour in self.target_hours:
        # 关键逻辑：数学计算时间轴位置
        hours_per_element = 24 / total_elements
        element_index = int(target_hour / hours_per_element)
        element_index = max(0, min(element_index, total_elements - 1))
        
        element = timeline_elements[element_index]['element']
        element.click()
```

### Chrome插件的时间轴逻辑（错误）：
```javascript
// 错误的逻辑：试图直接匹配小时数
for (let j = 0; j < timelineElements.length; j++) {
    if (timelineElements[j].hour === targetHour) {
        targetElement = timelineElements[j];
        elementIndex = j;
        break;
    }
}
```

## 修复方案

### 1. 修复时间轴元素查找
```javascript
async findTimelineElements() {
    const timelineElements = [];
    const allLinks = document.querySelectorAll('a');
    
    // 精确模拟Python脚本的条件
    for (const link of allLinks) {
        if (link.offsetParent !== null) { // 相当于Python的is_displayed()
            const rect = link.getBoundingClientRect();
            const location = { x: rect.left, y: rect.top };
            const size = { width: rect.width, height: rect.height };
            
            // 精确模拟Python脚本的筛选条件
            if (location.y > 800 && 
                size.width > 30 && size.width < 80 && 
                size.height > 20 && size.height < 50) {
                
                timelineElements.push({
                    element: link,
                    x_position: location.x
                });
            }
        }
    }
    
    // 精确模拟Python脚本的排序
    timelineElements.sort((a, b) => a.x_position - b.x_position);
    return timelineElements;
}
```

### 2. 修复时间轴切换逻辑
```javascript
async getDataForAllHours(dataType) {
    const timelineElements = await this.findTimelineElements();
    const totalElements = timelineElements.length;
    
    for (const targetHour of this.targetHours) {
        // 精确模拟Python脚本的数学计算
        const hoursPerElement = 24 / totalElements;
        let elementIndex = Math.floor(targetHour / hoursPerElement);
        elementIndex = Math.max(0, Math.min(elementIndex, totalElements - 1));
        
        const element = timelineElements[elementIndex].element;
        element.click();
        
        // 获取数据...
    }
}
```

## 修复文件

### 主要修复文件：
- `content-timeline-fixed.js` - 修复版的content script
- `manifest-fixed.json` - 使用修复版content script的manifest

### 使用方法：

1. **备份原文件**：
   ```bash
   cp content.js content.js.backup
   cp manifest.json manifest.json.backup
   ```

2. **使用修复版**：
   ```bash
   cp content-timeline-fixed.js content.js
   cp manifest-fixed.json manifest.json
   ```

3. **重新加载插件**：
   - 在Chrome扩展管理页面点击"重新加载"
   - 或者删除插件后重新安装

## 修复效果

### 修复前：
- ❌ 时间轴切换不准确
- ❌ 无法获取指定时间点的数据
- ❌ 数据提取失败率高

### 修复后：
- ✅ 精确按照6个时间点切换：02:00, 05:00, 08:00, 11:00, 17:00, 23:00
- ✅ 使用与Python脚本完全相同的时间轴计算逻辑
- ✅ 数据提取成功率大幅提升
- ✅ 与Python脚本结果保持一致

## 技术细节

### 关键修复点：

1. **时间轴元素识别**：
   - 移除了对文本格式的依赖（XX:XX格式）
   - 只使用位置和尺寸条件，与Python脚本保持一致

2. **时间轴索引计算**：
   - 使用数学计算：`Math.floor(targetHour / hoursPerElement)`
   - 而不是直接匹配小时数

3. **边界处理**：
   - 使用`Math.max(0, Math.min(elementIndex, totalElements - 1))`
   - 确保索引在有效范围内

4. **等待时间**：
   - 与Python脚本保持相同的等待时间（3秒）
   - 确保页面有足够时间更新

## 验证方法

1. **控制台日志**：
   - 查看时间轴元素数量是否正确
   - 确认计算的索引值是否合理

2. **数据对比**：
   - 与Python脚本获取的数据进行对比
   - 确保6个时间点的数据都能正确获取

3. **视觉验证**：
   - 观察时间轴是否按预期切换
   - 确认每次点击后地图时间显示是否正确

## 注意事项

1. **页面加载**：确保页面完全加载后再开始提取
2. **网络延迟**：根据网络情况适当调整等待时间
3. **元素变化**：如果Ventusky网站更新，可能需要调整筛选条件

---

**修复完成！现在Chrome插件的时间轴切换功能与Python脚本完全一致。**
