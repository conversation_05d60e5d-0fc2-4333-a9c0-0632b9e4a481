# 🌤️ Ventusky Weather Extractor Chrome插件

专业级天气数据提取工具，支持多日、多参数、多时间点天气预报获取。

## 📋 安装步骤

### 方法一：直接安装（推荐）

1. **打开Chrome扩展管理页面**
   - 在Chrome地址栏输入：`chrome://extensions/`
   - 或者点击Chrome菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 在页面右上角找到"开发者模式"开关
   - 点击开关启用开发者模式

3. **加载插件**
   - 点击"加载已解压的扩展程序"按钮
   - 选择 `chrome-extension` 文件夹
   - 点击"选择文件夹"

4. **验证安装**
   - 插件应该出现在扩展程序列表中
   - 浏览器工具栏会显示插件图标

### 方法二：创建图标后安装

如果遇到图标错误，请先创建图标：

1. **打开图标转换器**
   - 用浏览器打开 `icons/convert-icons.html`
   - 点击"生成所有尺寸的PNG图标"按钮

2. **下载图标文件**
   - 右键点击每个生成的图标
   - 选择"图片另存为"
   - 按照显示的文件名保存到 `icons` 文件夹

3. **更新manifest.json**
   - 在manifest.json中添加图标配置：
   ```json
   "icons": {
     "16": "icons/icon16.png",
     "32": "icons/icon32.png", 
     "48": "icons/icon48.png",
     "128": "icons/icon128.png"
   },
   "action": {
     "default_popup": "popup.html",
     "default_title": "Ventusky Weather Extractor",
     "default_icon": {
       "16": "icons/icon16.png",
       "32": "icons/icon32.png",
       "48": "icons/icon48.png", 
       "128": "icons/icon128.png"
     }
   }
   ```

4. **重新加载插件**
   - 在扩展程序页面点击插件的"刷新"按钮

## 🚀 使用方法

### 基本使用

1. **访问Ventusky网站**
   - 打开 https://www.ventusky.com
   - 插件图标会变为激活状态

2. **打开插件**
   - 点击浏览器工具栏中的插件图标
   - 或者右键点击页面选择插件

3. **配置提取参数**
   - **坐标设置**：输入纬度和经度，或点击"获取当前页面坐标"
   - **日期范围**：选择开始和结束日期，或使用快速选择按钮
   - **时间点**：选择需要的时间点（默认：02:00, 05:00, 08:00, 11:00, 17:00, 23:00）
   - **数据类型**：选择需要的天气参数（温度、降水量、云量、风速）

4. **开始提取**
   - 点击"开始提取"按钮
   - 插件会自动操作页面获取数据
   - 实时显示提取进度

5. **下载结果**
   - 提取完成后会显示结果统计
   - 点击相应按钮下载JSON、CSV或Excel格式的数据

### 高级功能

- **设置页面**：右键插件图标 → 选项，可配置默认参数
- **历史记录**：查看和管理之前的提取记录
- **批量导出**：支持多种格式的数据导出

## 🌟 主要功能

### ✅ 数据提取
- **4参数获取**：温度、降水量、云量、风速(100m)
- **6时间点**：02:00, 05:00, 08:00, 11:00, 17:00, 23:00
- **多日支持**：最多支持14天连续预报
- **智能分析**：自动生成综合天气状况

### ✅ 用户体验
- **可视化界面**：美观的弹窗和设置页面
- **实时进度**：详细的提取进度显示
- **错误处理**：完善的异常处理机制
- **数据验证**：自动验证数据合理性

### ✅ 数据管理
- **多格式导出**：JSON、CSV、Excel
- **历史记录**：完整的提取历史管理
- **设置同步**：Chrome账户同步设置
- **数据备份**：支持设置导入导出

## 🔧 技术特点

- **Manifest V3**：使用最新的Chrome扩展标准
- **无依赖**：纯JavaScript实现，无需外部库
- **高性能**：优化的数据提取算法
- **跨平台**：支持所有Chrome支持的操作系统

## 📊 数据格式

### JSON格式示例
```json
{
  "location": "30.1833°, 120.2°",
  "dateRange": {
    "start": "2025-08-04",
    "end": "2025-08-05"
  },
  "weatherData": {
    "2025-08-04": {
      "02:00": {
        "temperature": "26°C",
        "precipitation": "0 mm",
        "cloudcover": "45%",
        "windspeed": "12 km/h"
      }
    }
  }
}
```

## ❓ 常见问题

### Q: 插件无法加载？
A: 请确保：
1. 已启用开发者模式
2. 选择了正确的文件夹
3. 所有必需文件都存在

### Q: 提取失败？
A: 请检查：
1. 是否在Ventusky页面使用
2. 网络连接是否正常
3. 页面是否完全加载

### Q: 数据不准确？
A: 插件直接从Ventusky官方页面提取数据，准确性与官网一致。

## 🔄 更新日志

### v1.0.0 (2025-08-04)
- ✅ 初始版本发布
- ✅ 基础数据提取功能
- ✅ 多格式导出支持
- ✅ 设置和历史记录管理

## 📞 支持

如有问题或建议，请：
1. 检查本文档的常见问题部分
2. 查看浏览器控制台的错误信息
3. 提供详细的错误描述和复现步骤

---

**Ventusky Weather Extractor** - 让天气数据获取变得简单高效！ 🌤️
