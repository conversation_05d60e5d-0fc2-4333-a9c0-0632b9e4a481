<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ventusky Weather Extractor</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- 标题栏 -->
        <div class="header">
            <div class="logo">
                <span class="weather-icon">🌤️</span>
                <h1>天气数据提取器</h1>
            </div>
            <div class="version">v1.0.0</div>
        </div>

        <!-- 状态指示器 -->
        <div class="status-bar" id="statusBar">
            <span class="status-dot" id="statusDot"></span>
            <span class="status-text" id="statusText">等待操作</span>
        </div>

        <!-- 坐标设置 -->
        <div class="section">
            <h3>📍 位置坐标</h3>
            <div class="coordinate-input">
                <div class="input-group">
                    <label>纬度:</label>
                    <input type="number" id="latitude" value="30.1833" step="0.0001" min="-90" max="90">
                </div>
                <div class="input-group">
                    <label>经度:</label>
                    <input type="number" id="longitude" value="120.2" step="0.0001" min="-180" max="180">
                </div>
            </div>
            <button class="btn-secondary" id="getCurrentLocation">获取当前页面坐标</button>
        </div>

        <!-- 日期设置 -->
        <div class="section">
            <h3>📅 日期范围</h3>
            <div class="date-input">
                <div class="input-group">
                    <label>开始日期:</label>
                    <input type="date" id="startDate">
                </div>
                <div class="input-group">
                    <label>结束日期:</label>
                    <input type="date" id="endDate">
                </div>
            </div>
            <div class="quick-dates">
                <button class="btn-quick" data-days="1">今天</button>
                <button class="btn-quick" data-days="3">3天</button>
                <button class="btn-quick" data-days="7">7天</button>
                <button class="btn-quick" data-days="14">14天</button>
            </div>
        </div>

        <!-- 时间点选择 -->
        <div class="section">
            <h3>🕐 时间点</h3>
            <div class="time-selection">
                <label class="time-checkbox">
                    <input type="checkbox" value="2" checked>
                    <span>02:00</span>
                </label>
                <label class="time-checkbox">
                    <input type="checkbox" value="5" checked>
                    <span>05:00</span>
                </label>
                <label class="time-checkbox">
                    <input type="checkbox" value="8" checked>
                    <span>08:00</span>
                </label>
                <label class="time-checkbox">
                    <input type="checkbox" value="11" checked>
                    <span>11:00</span>
                </label>
                <label class="time-checkbox">
                    <input type="checkbox" value="17" checked>
                    <span>17:00</span>
                </label>
                <label class="time-checkbox">
                    <input type="checkbox" value="23" checked>
                    <span>23:00</span>
                </label>
            </div>
        </div>

        <!-- 数据类型选择 -->
        <div class="section">
            <h3>📊 数据类型</h3>
            <div class="data-types">
                <label class="data-checkbox">
                    <input type="checkbox" value="temperature" checked>
                    <span>🌡️ 温度</span>
                </label>
                <label class="data-checkbox">
                    <input type="checkbox" value="precipitation" checked>
                    <span>🌧️ 降水量</span>
                </label>
                <label class="data-checkbox">
                    <input type="checkbox" value="cloudcover" checked>
                    <span>☁️ 云量</span>
                </label>
                <label class="data-checkbox">
                    <input type="checkbox" value="windspeed" checked>
                    <span>💨 风速</span>
                </label>
            </div>
        </div>

        <!-- 进度显示 -->
        <div class="section" id="progressSection" style="display: none;">
            <h3>📈 提取进度</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">0/0 天完成</div>
        </div>

        <!-- 操作按钮 -->
        <div class="actions">
            <button class="btn-primary" id="startExtraction">
                <span class="btn-icon">🚀</span>
                开始提取
            </button>
            <button class="btn-secondary" id="stopExtraction" style="display: none;">
                <span class="btn-icon">⏹️</span>
                停止提取
            </button>
        </div>

        <!-- 结果区域 -->
        <div class="section" id="resultsSection" style="display: none;">
            <h3>📁 提取结果</h3>
            <div class="results-info" id="resultsInfo"></div>
            <div class="download-buttons" id="downloadButtons"></div>
        </div>

        <!-- 底部链接 -->
        <div class="footer">
            <a href="#" id="openOptions">⚙️ 设置</a>
            <a href="#" id="viewHistory">📊 历史记录</a>
            <a href="#" id="helpLink">❓ 帮助</a>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
