// Ventusky Weather Extractor - Background Service Worker (简化版)

// 插件安装事件
chrome.runtime.onInstalled.addListener((details) => {
    console.log('Ventusky Weather Extractor 已安装');
    
    // 初始化默认设置
    const defaultSettings = {
        defaultLat: 30.1833,
        defaultLon: 120.2,
        autoSave: true,
        maxHistoryItems: 50
    };
    
    chrome.storage.sync.get(Object.keys(defaultSettings)).then((result) => {
        const updates = {};
        for (const [key, defaultValue] of Object.entries(defaultSettings)) {
            if (result[key] === undefined) {
                updates[key] = defaultValue;
            }
        }
        if (Object.keys(updates).length > 0) {
            chrome.storage.sync.set(updates);
        }
    });
});

// 消息处理
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Background收到消息:', message);
    
    switch (message.action) {
        case 'saveExtractionResult':
            saveExtractionResult(message.data).then(() => {
                sendResponse({ success: true });
            }).catch((error) => {
                sendResponse({ success: false, error: error.message });
            });
            return true;
            
        case 'getExtractionHistory':
            getExtractionHistory().then((history) => {
                sendResponse({ success: true, history });
            }).catch((error) => {
                sendResponse({ success: false, error: error.message });
            });
            return true;
            
        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
});

// 保存提取结果
async function saveExtractionResult(data) {
    try {
        const result = await chrome.storage.local.get(['extractionHistory']);
        const history = result.extractionHistory || [];
        
        const newRecord = {
            id: Date.now(),
            timestamp: new Date().toISOString(),
            location: data.location,
            dateRange: data.dateRange,
            totalDays: data.totalDays,
            successfulDays: Object.keys(data.weatherData || {}).length,
            data: data
        };
        
        history.unshift(newRecord);
        
        // 限制历史记录数量
        if (history.length > 50) {
            history.splice(50);
        }
        
        await chrome.storage.local.set({ extractionHistory: history });
        console.log('提取结果已保存');
        
    } catch (error) {
        console.error('保存提取结果失败:', error);
        throw error;
    }
}

// 获取提取历史
async function getExtractionHistory() {
    try {
        const result = await chrome.storage.local.get(['extractionHistory']);
        return result.extractionHistory || [];
    } catch (error) {
        console.error('获取历史记录失败:', error);
        return [];
    }
}

// 标签页更新事件
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        if (tab.url.includes('ventusky.com')) {
            chrome.action.setTitle({
                tabId: tabId,
                title: 'Ventusky Weather Extractor - 点击开始提取'
            });
        } else {
            chrome.action.setTitle({
                tabId: tabId,
                title: 'Ventusky Weather Extractor - 请在Ventusky页面使用'
            });
        }
    }
});

console.log('Background script loaded');
