# Ventusky Chrome插件安装和使用指南

## 🔧 时间轴修复版安装

### 步骤1: 备份原文件
```bash
cd VentuskyWeatherSystem/chrome-extension/
cp content.js content.js.backup
cp manifest.json manifest.json.backup
```

### 步骤2: 使用修复版文件
```bash
# 方法1: 直接替换
cp content-timeline-fixed.js content.js
cp manifest-fixed.json manifest.json

# 方法2: 或者在manifest.json中修改引用
# 将 "content.js" 改为 "content-timeline-fixed.js"
```

### 步骤3: 安装Chrome插件

1. **打开Chrome扩展管理页面**：
   - 地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**：
   - 点击右上角的"开发者模式"开关

3. **加载插件**：
   - 点击"加载已解压的扩展程序"
   - 选择 `VentuskyWeatherSystem/chrome-extension/` 文件夹

4. **验证安装**：
   - 确认插件出现在扩展列表中
   - 插件名称：Ventusky Weather Extractor
   - 版本：1.0.0

## 🌤️ 使用方法

### 基本使用流程

1. **访问Ventusky网站**：
   ```
   https://www.ventusky.com/
   ```

2. **设置位置和日期**：
   - 在地图上点击或搜索目标位置
   - 选择要查询的日期

3. **启动数据提取**：
   - 点击浏览器工具栏中的插件图标
   - 在弹出窗口中点击"开始提取"

4. **等待提取完成**：
   - 插件会自动切换数据类型（温度→降水量→云量→风速）
   - 每种数据类型会获取6个时间点的数据
   - 右上角会显示进度提示

5. **查看结果**：
   - 提取完成后会显示完整的天气数据
   - 可以复制JSON数据或下载文件

### 获取的数据格式

```json
{
  "location": {
    "lat": 30.1833,
    "lon": 120.2
  },
  "date": "2025-08-04",
  "data": {
    "02:00": {
      "temperature": "25°C",
      "precipitation": "0mm",
      "cloudcover": "30%",
      "windspeed": "12km/h"
    },
    "05:00": {
      "temperature": "23°C",
      "precipitation": "0.5mm",
      "cloudcover": "45%",
      "windspeed": "8km/h"
    },
    "08:00": {
      "temperature": "28°C",
      "precipitation": "0mm",
      "cloudcover": "20%",
      "windspeed": "15km/h"
    },
    "11:00": {
      "temperature": "32°C",
      "precipitation": "0mm",
      "cloudcover": "10%",
      "windspeed": "18km/h"
    },
    "17:00": {
      "temperature": "35°C",
      "precipitation": "2.3mm",
      "cloudcover": "60%",
      "windspeed": "22km/h"
    },
    "23:00": {
      "temperature": "27°C",
      "precipitation": "1.2mm",
      "cloudcover": "40%",
      "windspeed": "10km/h"
    }
  },
  "extractedAt": "2025-08-04T14:30:00.000Z"
}
```

## 🧪 测试和验证

### 控制台测试
1. **打开开发者工具**：
   - 按F12或右键→检查

2. **运行测试脚本**：
   ```javascript
   // 复制test-timeline.js的内容到控制台运行
   // 或者直接调用：
   window.timelineTest.runAllTests();
   ```

3. **查看测试结果**：
   - 确认找到正确数量的时间轴元素
   - 验证索引计算是否正确
   - 观察元素高亮显示

### 数据验证
1. **与Python脚本对比**：
   - 使用相同的位置和日期
   - 对比6个时间点的数据
   - 确认数据一致性

2. **手动验证**：
   - 观察时间轴切换是否正确
   - 确认每个时间点的地图显示
   - 验证提取的数据是否合理

## ⚠️ 注意事项

### 使用限制
- **网站兼容性**：仅适用于www.ventusky.com
- **浏览器要求**：Chrome 88+或基于Chromium的浏览器
- **网络要求**：需要稳定的网络连接

### 常见问题

1. **时间轴元素未找到**：
   - 确保页面完全加载
   - 检查网站是否有更新
   - 尝试刷新页面后重新提取

2. **数据提取失败**：
   - 检查网络连接
   - 确认目标位置有数据
   - 查看控制台错误信息

3. **插件无响应**：
   - 重新加载插件
   - 清除浏览器缓存
   - 检查插件权限设置

### 故障排除

1. **查看控制台日志**：
   ```javascript
   // 在控制台中查看详细日志
   console.log('检查插件状态');
   ```

2. **重置插件**：
   ```bash
   # 恢复备份文件
   cp content.js.backup content.js
   cp manifest.json.backup manifest.json
   
   # 重新应用修复
   cp content-timeline-fixed.js content.js
   ```

3. **联系支持**：
   - 提供控制台错误信息
   - 说明使用的浏览器版本
   - 描述具体的问题现象

## 🔄 更新和维护

### 定期检查
- Ventusky网站结构变化
- Chrome浏览器更新兼容性
- 插件功能正常性

### 版本更新
- 备份当前配置
- 下载最新版本
- 测试新功能
- 恢复个人设置

---

**现在您可以享受修复后的时间轴功能，获取准确的6个时间点天气数据！** 🌤️
