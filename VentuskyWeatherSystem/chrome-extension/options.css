/* Ventusky Weather Extractor - Options Page Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 标题栏 */
.header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.weather-icon {
    font-size: 28px;
}

.logo h1 {
    font-size: 24px;
    font-weight: 600;
}

.version {
    background: rgba(255, 255, 255, 0.2);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 500;
}

/* 导航标签 */
.tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0 30px;
}

.tab-btn {
    background: none;
    border: none;
    padding: 15px 20px;
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    color: #495057;
    background: rgba(102, 126, 234, 0.1);
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: white;
}

/* 内容区域 */
.content {
    padding: 30px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 区域样式 */
.section {
    margin-bottom: 40px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.section h2 {
    font-size: 18px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.description {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.5;
}

/* 表单组 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 6px;
}

.form-group input[type="number"],
.form-group input[type="text"],
.form-group select {
    width: 100%;
    max-width: 300px;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.help-text {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

/* 复选框样式 */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px 0;
    font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #667eea;
}

.checkmark {
    font-size: 14px;
    color: #495057;
}

/* 预设位置按钮 */
.preset-locations h3 {
    font-size: 16px;
    margin: 20px 0 12px 0;
    color: #495057;
}

.location-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.location-btn {
    padding: 10px 15px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.location-btn:hover {
    border-color: #667eea;
    background: #f8f9ff;
    color: #667eea;
}

/* 时间和数据类型复选框网格 */
.time-checkboxes,
.data-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

/* 按钮样式 */
.btn-primary,
.btn-secondary,
.btn-danger {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 10px;
    margin-bottom: 10px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* 信息卡片 */
.info-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    margin-bottom: 20px;
}

.info-card h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 16px;
}

.info-card ul {
    list-style: none;
    padding: 0;
}

.info-card li {
    padding: 4px 0;
    color: #6c757d;
}

/* 统计网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.stat-value {
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 链接样式 */
.links {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.links a {
    color: #667eea;
    text-decoration: none;
    padding: 8px 12px;
    border: 1px solid #667eea;
    border-radius: 4px;
    font-size: 13px;
    transition: all 0.2s ease;
}

.links a:hover {
    background: #667eea;
    color: white;
}

/* 底部操作栏 */
.footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status {
    font-size: 14px;
    color: #6c757d;
}

.status.success {
    color: #28a745;
}

.status.error {
    color: #dc3545;
}

.actions {
    display: flex;
    gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 0;
    }
    
    .header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .tabs {
        padding: 0 20px;
        overflow-x: auto;
    }
    
    .tab-btn {
        white-space: nowrap;
        min-width: 100px;
    }
    
    .content {
        padding: 20px;
    }
    
    .section {
        padding: 20px;
    }
    
    .location-buttons {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
    
    .time-checkboxes,
    .data-checkboxes {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
    
    .footer {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
    }
    
    .actions {
        width: 100%;
        justify-content: center;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tab-content.active {
    animation: fadeIn 0.3s ease-out;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
    
    .container {
        background: #2c3e50;
        color: #ecf0f1;
    }
    
    .section {
        background: #34495e;
        border-color: #4a5f7a;
    }
    
    .section h2 {
        color: #ecf0f1;
    }
    
    .form-group input,
    .form-group select {
        background: #34495e;
        border-color: #4a5f7a;
        color: #ecf0f1;
    }
    
    .info-card {
        background: #34495e;
        border-color: #4a5f7a;
    }
}
