/* Ventusky Weather Extractor - Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    width: 380px;
    min-height: 600px;
}

.container {
    background: white;
    border-radius: 12px;
    margin: 8px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 标题栏 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.weather-icon {
    font-size: 24px;
}

.logo h1 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.version {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* 状态栏 */
.status-bar {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6c757d;
}

.status-dot.ready { background: #28a745; }
.status-dot.working { background: #ffc107; animation: pulse 1.5s infinite; }
.status-dot.error { background: #dc3545; }
.status-dot.success { background: #28a745; }

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-text {
    font-size: 14px;
    font-weight: 500;
}

/* 区域样式 */
.section {
    margin-bottom: 20px;
}

.section h3 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
}

/* 坐标输入 */
.coordinate-input {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 10px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.input-group label {
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
}

.input-group input {
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* 日期输入 */
.date-input {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 10px;
}

.quick-dates {
    display: flex;
    gap: 6px;
}

.btn-quick {
    flex: 1;
    padding: 6px 8px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-quick:hover {
    background: #e9ecef;
}

.btn-quick.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* 时间点选择 */
.time-selection {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
}

.time-checkbox {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 10px;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 13px;
}

.time-checkbox:hover {
    background: #e9ecef;
}

.time-checkbox input[type="checkbox"] {
    margin: 0;
}

/* 数据类型选择 */
.data-types {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.data-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 13px;
}

.data-checkbox:hover {
    background: #e9ecef;
}

.data-checkbox input[type="checkbox"] {
    margin: 0;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

/* 按钮样式 */
.actions {
    display: flex;
    gap: 10px;
    margin: 20px 0;
}

.btn-primary, .btn-secondary {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-secondary {
    background: #f8f9fa;
    color: #495057;
    border: 2px solid #dee2e6;
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

/* 结果区域 */
.results-info {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 12px;
    font-size: 13px;
    color: #155724;
}

.download-buttons {
    display: flex;
    gap: 8px;
}

.download-buttons button {
    flex: 1;
    padding: 8px 12px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.download-buttons button:hover {
    background: #218838;
}

/* 底部链接 */
.footer {
    display: flex;
    justify-content: space-around;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    margin-top: 20px;
}

.footer a {
    color: #6c757d;
    text-decoration: none;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s;
}

.footer a:hover {
    color: #495057;
    background: #f8f9fa;
}
