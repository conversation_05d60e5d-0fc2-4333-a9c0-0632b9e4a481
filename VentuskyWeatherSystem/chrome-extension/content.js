// Ventusky Weather Extractor - 简单修复版
// 基于原始可用版本，只修复风速100米问题

class VentuskyWeatherExtractor {
    constructor() {
        this.isExtracting = false;
        this.currentDateIndex = 0;
        this.allWeatherData = {};
        this.config = null;
        this.targetHours = [2, 5, 8, 11, 17, 23];
        
        this.setupMessageListener();
        this.injectStyles();
        
        console.log('🌤️ Ventusky Weather Extractor Content Script 已加载 (简单修复版)');
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('📨 Content script收到消息:', message);
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
    }

    injectStyles() {
        if (document.getElementById('weather-extractor-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'weather-extractor-styles';
        style.textContent = `
            .weather-extractor-overlay {
                position: fixed !important;
                top: 20px !important;
                right: 20px !important;
                background: rgba(0, 0, 0, 0.9) !important;
                color: white !important;
                padding: 20px !important;
                border-radius: 12px !important;
                z-index: 999999 !important;
                font-family: Arial, sans-serif !important;
                font-size: 14px !important;
                max-width: 320px !important;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
            }
            .weather-extractor-progress {
                margin-top: 15px !important;
                background: rgba(255, 255, 255, 0.1) !important;
                height: 6px !important;
                border-radius: 3px !important;
                overflow: hidden !important;
            }
            .weather-extractor-progress-bar {
                background: #4CAF50 !important;
                height: 100% !important;
                transition: width 0.4s ease !important;
            }
        `;
        document.head.appendChild(style);
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'getCurrentCoordinates':
                    const coords = this.getCurrentCoordinates();
                    console.log('✅ 从URL获取坐标:', coords.lat, coords.lon);
                    sendResponse(coords);
                    break;
                    
                case 'startExtraction':
                    if (this.isExtracting) {
                        sendResponse({ success: false, error: '提取正在进行中' });
                        return;
                    }
                    console.log('🚀 开始天气数据提取 (简单修复版):', message.config);
                    const result = await this.startExtraction(message.config);
                    sendResponse(result);
                    break;
                    
                case 'stopExtraction':
                    this.stopExtraction();
                    sendResponse({ success: true });
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('❌ Content script error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    getCurrentCoordinates() {
        try {
            const url = new URL(window.location.href);
            const params = url.searchParams.get('p');
            
            if (params) {
                const coords = params.split(';');
                if (coords.length >= 2) {
                    return {
                        lat: parseFloat(coords[0]),
                        lon: parseFloat(coords[1])
                    };
                }
            }
            
            return { lat: null, lon: null };
        } catch (error) {
            console.error('❌ 获取坐标失败:', error);
            return { lat: null, lon: null };
        }
    }

    async startExtraction(config) {
        this.isExtracting = true;
        this.config = config;
        
        try {
            this.showOverlay('正在初始化...');
            
            // 等待页面加载
            await this.sleep(3000);
            
            // 获取温度数据（默认图层）
            console.log('🌡️ 获取温度数据...');
            this.updateOverlay('获取温度数据...', 20);
            const tempData = await this.getDataForAllHours("温度");
            
            // 获取降水量数据
            console.log('🌧️ 获取降水量数据...');
            this.updateOverlay('获取降水量数据...', 40);
            await this.switchToDataType("降水量");
            const precipData = await this.getDataForAllHours("降水量");
            
            // 获取云量数据
            console.log('☁️ 获取云量数据...');
            this.updateOverlay('获取云量数据...', 60);
            await this.switchToDataType("云量");
            const cloudData = await this.getDataForAllHours("云量");
            
            // 获取风速数据（修复版）
            console.log('💨 获取风速数据...');
            this.updateOverlay('获取风速数据...', 80);
            await this.switchToDataType("风速");
            const windData = await this.getDataForAllHours("风速");
            
            // 整合数据
            this.updateOverlay('整合数据...', 90);
            const finalResult = this.integrateWeatherData(tempData, precipData, cloudData, windData);
            
            this.updateOverlay('提取完成！', 100);
            
            console.log('🎉 天气数据提取完成:', finalResult);
            
            // 发送数据到popup
            chrome.runtime.sendMessage({
                action: 'extractionComplete',
                data: finalResult
            });
            
            this.hideOverlay();
            this.isExtracting = false;
            
            return { success: true, data: finalResult };
            
        } catch (error) {
            console.error('❌ 天气数据提取失败:', error);
            this.hideOverlay();
            this.isExtracting = false;
            
            return { success: false, error: error.message };
        }
    }

    async switchToDataType(dataType) {
        console.log(`🔄 切换到${dataType}数据类型`);
        
        try {
            if (dataType === "降水量") {
                return await this.switchToPrecipitation();
            } else if (dataType === "云量") {
                return await this.switchToCloudcover();
            } else if (dataType === "风速") {
                return await this.switchToWindspeedSimple(); // 使用简单修复版
            }
            
            return true;
        } catch (error) {
            console.error(`❌ 切换到${dataType}失败:`, error);
            return false;
        }
    }

    async switchToPrecipitation() {
        console.log('🌧️ 切换到降水量图层');
        
        try {
            const elements = document.querySelectorAll('*');
            
            for (const element of elements) {
                const text = element.textContent || '';
                if (text.includes('降水量') && element.offsetParent !== null) {
                    
                    console.log(`🎯 找到降水量选项:`, element, text);
                    
                    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    await this.sleep(1000);
                    
                    element.click();
                    await this.sleep(5000);
                    
                    console.log('✅ 成功切换到降水量图层');
                    return true;
                }
            }
            
            console.log('❌ 未找到降水量选项');
            return false;
        } catch (error) {
            console.error('❌ 切换到降水量失败:', error);
            return false;
        }
    }

    async switchToCloudcover() {
        console.log('☁️ 切换到云量图层');
        
        try {
            const elements = document.querySelectorAll('*');
            
            for (const element of elements) {
                const text = element.textContent || '';
                if (text.includes('云量') && element.offsetParent !== null) {
                    
                    console.log(`🎯 找到云量选项:`, element, text);
                    
                    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    await this.sleep(1000);
                    
                    element.click();
                    await this.sleep(5000);
                    
                    console.log('✅ 成功切换到云量图层');
                    return true;
                }
            }
            
            console.log('❌ 未找到云量选项');
            return false;
        } catch (error) {
            console.error('❌ 切换到云量失败:', error);
            return false;
        }
    }

    // 简单修复版的风速切换
    async switchToWindspeedSimple() {
        console.log('💨 切换到风速图层（简单修复版）');
        
        try {
            // 第一步：点击风速选项
            const elements = document.querySelectorAll('*');
            let windClicked = false;
            
            for (const element of elements) {
                const text = element.textContent || '';
                if (text.includes('风速') && element.offsetParent !== null) {
                    
                    console.log(`🎯 找到风速选项:`, element, text);
                    
                    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    await this.sleep(1000);
                    
                    element.click();
                    await this.sleep(3000);
                    
                    console.log('✅ 成功点击风速选项');
                    windClicked = true;
                    break;
                }
            }
            
            if (!windClicked) {
                console.log('❌ 未找到风速选项');
                return false;
            }
            
            // 第二步：简单的100米高度选择
            console.log('🏔️ 开始选择100米高度...');
            
            // 等待菜单展开
            await this.sleep(2000);
            
            // 查找并点击"地上10米"（下拉框）
            console.log('🔍 查找地上10米下拉框...');
            const heightElements = document.querySelectorAll('*');
            
            for (const element of heightElements) {
                const text = element.textContent || '';
                if (text.includes('地上10米') && element.offsetParent !== null) {
                    
                    console.log(`🎯 找到地上10米下拉框: "${text}"`);
                    
                    try {
                        element.click();
                        await this.sleep(2000);
                        console.log('✅ 成功点击地上10米下拉框');
                        break;
                    } catch (error) {
                        console.log(`❌ 点击地上10米下拉框失败: ${error.message}`);
                        continue;
                    }
                }
            }
            
            // 查找并点击"地上100米"选项
            console.log('🔍 查找地上100米选项...');
            await this.sleep(1000);
            
            const updatedElements = document.querySelectorAll('*');
            
            for (const element of updatedElements) {
                const text = element.textContent || '';
                if ((text.includes('地上100米') || text.includes('100米')) && element.offsetParent !== null) {
                    
                    console.log(`🎯 找到精确的地上100米链接: "${text}"`);
                    
                    try {
                        element.click();
                        await this.sleep(2000);
                        console.log('✅ 成功选择地上100米');
                        break;
                    } catch (error) {
                        console.log(`❌ 选择100米选项失败: ${error.message}`);
                        continue;
                    }
                }
            }
            
            console.log('✅ 成功切换到风速图层');
            return true;
            
        } catch (error) {
            console.error('❌ 切换到风速失败:', error);
            return false;
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 时间轴查找和切换方法
    async getDataForAllHours(dataType) {
        console.log(`📊 获取${dataType}的所有时间点数据`);

        const timelineElements = await this.findTimelineElements();
        if (!timelineElements.length) {
            console.log('❌ 未找到时间轴元素');
            return {};
        }

        const totalElements = timelineElements.length;
        const dataResults = {};

        console.log(`📊 开始遍历${this.targetHours.length}个目标时间点`);

        for (const targetHour of this.targetHours) {
            try {
                console.log(`🕐 处理${targetHour}点数据...`);

                // 使用Python脚本的时间轴切换逻辑
                const hoursPerElement = 24 / totalElements;
                let elementIndex = Math.floor(targetHour / hoursPerElement);
                elementIndex = Math.max(0, Math.min(elementIndex, totalElements - 1));

                const element = timelineElements[elementIndex].element;

                console.log(`🎯 点击时间轴元素 ${elementIndex}/${totalElements-1}:`, {
                    targetHour: targetHour,
                    hoursPerElement: hoursPerElement.toFixed(2),
                    calculatedIndex: elementIndex,
                    x_position: Math.round(timelineElements[elementIndex].x_position),
                    text: element.textContent || ''
                });

                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await this.sleep(1000);

                element.click();
                await this.sleep(3000);

                // 悬停获取数据
                await this.hoverMapCenter();

                // 提取数据
                let hourData = null;
                if (dataType === "温度") {
                    hourData = this.extractTemperatureData();
                } else if (dataType === "降水量") {
                    hourData = this.extractPrecipitationData();
                } else if (dataType === "云量") {
                    hourData = this.extractCloudcoverData();
                } else if (dataType === "风速") {
                    hourData = this.extractWindspeedData();
                }

                if (hourData) {
                    const timeKey = `${targetHour.toString().padStart(2, '0')}:00`;
                    dataResults[timeKey] = hourData;
                    console.log(`✅ ${timeKey} ${dataType}数据:`, hourData);
                } else {
                    console.log(`⚠️ ${targetHour}点${dataType}数据提取失败`);
                }

            } catch (error) {
                console.error(`❌ 获取${targetHour}点数据失败:`, error);
                continue;
            }
        }

        console.log(`📊 ${dataType}数据提取完成，成功获取${Object.keys(dataResults).length}/${this.targetHours.length}个时间点:`, dataResults);
        return dataResults;
    }

    async findTimelineElements() {
        console.log('🔍 查找时间轴元素...');

        const timelineElements = [];
        const allLinks = document.querySelectorAll('a');

        console.log(`🔍 检查${allLinks.length}个链接元素`);

        for (const link of allLinks) {
            try {
                if (link.offsetParent !== null) {
                    const rect = link.getBoundingClientRect();
                    const location = { x: rect.left, y: rect.top };
                    const size = { width: rect.width, height: rect.height };

                    // 使用宽松的筛选条件
                    if (location.y > 700 &&
                        size.width > 20 && size.width < 100 &&
                        size.height > 15 && size.height < 60) {

                        timelineElements.push({
                            element: link,
                            x_position: location.x
                        });

                        console.log('✅ 找到时间轴元素:', {
                            x_position: location.x,
                            y: location.y,
                            width: size.width,
                            height: size.height,
                            text: link.textContent || ''
                        });
                    }
                }
            } catch {
                continue;
            }
        }

        // 按x坐标排序
        timelineElements.sort((a, b) => a.x_position - b.x_position);

        console.log(`✅ 找到${timelineElements.length}个时间轴元素`);

        return timelineElements;
    }

    async hoverMapCenter() {
        console.log('🎯 悬停在地图中心');

        try {
            const canvas = document.querySelector('canvas');
            if (!canvas) {
                console.log('❌ 未找到canvas元素');
                return;
            }

            const rect = canvas.getBoundingClientRect();
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const event = new MouseEvent('mouseover', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + centerX,
                clientY: rect.top + centerY
            });

            canvas.dispatchEvent(event);
            await this.sleep(3000);

        } catch (error) {
            console.error('❌ 悬停失败:', error);
        }
    }

    // 数据提取方法
    extractTemperatureData() {
        try {
            const allElements = document.querySelectorAll('*');

            for (const element of allElements) {
                if (element.offsetParent !== null) {
                    const text = element.textContent || element.innerText || '';

                    const tempMatch = text.match(/(-?\d+)°/);
                    if (tempMatch) {
                        const tempValue = parseInt(tempMatch[1]);

                        if (tempValue >= -50 && tempValue <= 60) {
                            return {
                                temperature: tempValue + '°C',
                                originalText: text,
                                extractedText: tempMatch[1]
                            };
                        }
                    }
                }
            }

            return null;
        } catch (error) {
            console.error('❌ 提取温度数据失败:', error);
            return null;
        }
    }

    extractPrecipitationData() {
        try {
            const allElements = document.querySelectorAll('*');

            for (const element of allElements) {
                if (element.offsetParent !== null) {
                    const text = element.textContent || element.innerText || '';

                    const precipMatch = text.match(/(\d+(?:[.,]\d+)?)(?:\s*mm)?/);
                    if (precipMatch) {
                        let precipValue = precipMatch[1].replace(',', '.');
                        precipValue = parseFloat(precipValue);

                        if (precipValue >= 0 && precipValue <= 200) {
                            return {
                                precipitation: precipValue + 'mm',
                                originalText: text,
                                extractedText: precipMatch[1]
                            };
                        }
                    }
                }
            }

            return { precipitation: '0mm' };
        } catch (error) {
            console.error('❌ 提取降水量数据失败:', error);
            return { precipitation: '0mm' };
        }
    }

    extractCloudcoverData() {
        try {
            const allElements = document.querySelectorAll('*');

            for (const element of allElements) {
                if (element.offsetParent !== null) {
                    const text = element.textContent || element.innerText || '';

                    const cloudMatch = text.match(/(\d+)\s*%/);
                    if (cloudMatch) {
                        const cloudValue = parseInt(cloudMatch[1]);

                        if (cloudValue >= 0 && cloudValue <= 100) {
                            return {
                                cloudcover: cloudValue + '%',
                                originalText: text,
                                extractedText: cloudMatch[1]
                            };
                        }
                    }
                }
            }

            return { cloudcover: '0%' };
        } catch (error) {
            console.error('❌ 提取云量数据失败:', error);
            return { cloudcover: '0%' };
        }
    }

    extractWindspeedData() {
        try {
            const allElements = document.querySelectorAll('*');

            for (const element of allElements) {
                if (element.offsetParent !== null) {
                    const text = element.textContent || element.innerText || '';

                    const windMatch = text.match(/(\d+(?:\.\d+)?)\s*(?:km\/h|m\/s)/);
                    if (windMatch) {
                        let windValue = parseFloat(windMatch[1]);

                        if (text.includes('m/s')) {
                            windValue = windValue * 3.6;
                        }

                        if (windValue >= 0 && windValue <= 200) {
                            return {
                                windspeed: Math.round(windValue) + 'km/h',
                                originalText: text,
                                extractedText: windMatch[1]
                            };
                        }
                    }
                }
            }

            return { windspeed: '0km/h' };
        } catch (error) {
            console.error('❌ 提取风速数据失败:', error);
            return { windspeed: '0km/h' };
        }
    }

    integrateWeatherData(tempData, precipData, cloudData, windData) {
        const integratedData = {};

        for (const hour of this.targetHours) {
            const timeKey = `${hour.toString().padStart(2, '0')}:00`;

            integratedData[timeKey] = {
                temperature: tempData[timeKey]?.temperature || 'N/A',
                precipitation: precipData[timeKey]?.precipitation || '0mm',
                cloudcover: cloudData[timeKey]?.cloudcover || '0%',
                windspeed: windData[timeKey]?.windspeed || '0km/h'
            };
        }

        return {
            location: this.getCurrentCoordinates(),
            date: new Date().toISOString().split('T')[0],
            data: integratedData,
            extractedAt: new Date().toISOString()
        };
    }

    showOverlay(message) {
        this.hideOverlay();

        const overlay = document.createElement('div');
        overlay.id = 'weather-extractor-overlay';
        overlay.className = 'weather-extractor-overlay';
        overlay.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px;">🌤️ Ventusky Weather Extractor</div>
            <div id="weather-extractor-message">${message}</div>
            <div class="weather-extractor-progress">
                <div id="weather-extractor-progress-bar" class="weather-extractor-progress-bar" style="width: 0%"></div>
            </div>
        `;

        document.body.appendChild(overlay);
    }

    updateOverlay(message, progress) {
        const messageEl = document.getElementById('weather-extractor-message');
        const progressBar = document.getElementById('weather-extractor-progress-bar');

        if (messageEl) messageEl.textContent = message;
        if (progressBar) progressBar.style.width = progress + '%';
    }

    hideOverlay() {
        const overlay = document.getElementById('weather-extractor-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    stopExtraction() {
        this.isExtracting = false;
        this.hideOverlay();
        console.log('⏹️ 天气数据提取已停止');
    }
}

// 初始化
const extractor = new VentuskyWeatherExtractor();

// 添加简单的调试函数
window.ventuskyDebug = {
    findTimelineElements: () => extractor.findTimelineElements(),
    switchToWindspeed: () => extractor.switchToWindspeedSimple(),
    getCurrentCoordinates: () => extractor.getCurrentCoordinates(),
    testExtraction: () => extractor.startExtraction({ lat: 30.183, lon: 120.2 })
};

console.log('💡 简单修复版调试提示:');
console.log('  - window.ventuskyDebug.getCurrentCoordinates() // 测试坐标获取');
console.log('  - window.ventuskyDebug.findTimelineElements() // 测试时间轴查找');
console.log('  - window.ventuskyDebug.switchToWindspeed() // 测试风速切换');
console.log('  - window.ventuskyDebug.testExtraction() // 测试完整提取');
