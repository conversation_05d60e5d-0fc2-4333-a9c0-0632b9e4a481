// Ventusky Weather Extractor - 完全修复版 v2
// 基于Python脚本的精确逻辑重写

class VentuskyWeatherExtractorV2 {
    constructor() {
        this.isExtracting = false;
        this.currentDateIndex = 0;
        this.allWeatherData = {};
        this.config = null;
        this.targetHours = [2, 5, 8, 11, 17, 23]; // 与Python脚本完全相同
        
        this.setupMessageListener();
        this.injectStyles();
        
        console.log('🌤️ Ventusky Weather Extractor V2 已加载 (完全修复版)');
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('📨 Content script收到消息:', message);
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
    }

    injectStyles() {
        if (document.getElementById('weather-extractor-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'weather-extractor-styles';
        style.textContent = `
            .weather-extractor-overlay {
                position: fixed !important;
                top: 20px !important;
                right: 20px !important;
                background: rgba(0, 0, 0, 0.9) !important;
                color: white !important;
                padding: 20px !important;
                border-radius: 12px !important;
                z-index: 999999 !important;
                font-family: Arial, sans-serif !important;
                font-size: 14px !important;
                max-width: 320px !important;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
            }
            .weather-extractor-progress {
                margin-top: 15px !important;
                background: rgba(255, 255, 255, 0.1) !important;
                height: 6px !important;
                border-radius: 3px !important;
                overflow: hidden !important;
            }
            .weather-extractor-progress-bar {
                background: #4CAF50 !important;
                height: 100% !important;
                transition: width 0.4s ease !important;
            }
        `;
        document.head.appendChild(style);
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'getCurrentCoordinates':
                    const coords = this.getCurrentCoordinates();
                    sendResponse(coords);
                    break;
                    
                case 'startExtraction':
                    if (this.isExtracting) {
                        sendResponse({ success: false, error: '提取正在进行中' });
                        return;
                    }
                    const result = await this.startExtraction(message.config);
                    sendResponse(result);
                    break;
                    
                case 'stopExtraction':
                    this.stopExtraction();
                    sendResponse({ success: true });
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('❌ Content script error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    getCurrentCoordinates() {
        try {
            const url = new URL(window.location.href);
            const params = url.searchParams.get('p');
            
            if (params) {
                const coords = params.split(';');
                if (coords.length >= 2) {
                    return {
                        lat: parseFloat(coords[0]),
                        lon: parseFloat(coords[1])
                    };
                }
            }
            
            return { lat: null, lon: null };
        } catch (error) {
            console.error('❌ 获取坐标失败:', error);
            return { lat: null, lon: null };
        }
    }

    // 精确模拟Python脚本的时间轴查找逻辑
    async findTimelineElements() {
        console.log('🔍 查找时间轴元素...');
        
        const timelineElements = [];
        const allLinks = document.querySelectorAll('a');
        
        console.log(`🔍 检查${allLinks.length}个链接元素`);
        
        // 先收集所有底部元素进行分析
        const bottomElements = [];
        
        for (const link of allLinks) {
            try {
                if (link.offsetParent !== null) { // 相当于Python的is_displayed()
                    const rect = link.getBoundingClientRect();
                    const location = { x: rect.left, y: rect.top };
                    const size = { width: rect.width, height: rect.height };
                    
                    // 收集页面底部的所有元素用于分析
                    if (location.y > 700) {
                        bottomElements.push({
                            element: link,
                            x_position: location.x,
                            y_position: location.y,
                            width: size.width,
                            height: size.height,
                            text: link.textContent || '',
                            className: link.className || ''
                        });
                    }
                }
            } catch {
                continue;
            }
        }
        
        console.log(`🔍 页面底部(y>700)找到${bottomElements.length}个元素`);
        
        // 尝试多种筛选条件，从严格到宽松
        const filterConditions = [
            // 条件1: Python脚本的原始条件
            {
                name: 'Python原始条件',
                filter: (item) => item.y_position > 800 && 
                                 item.width > 30 && item.width < 80 && 
                                 item.height > 20 && item.height < 50
            },
            // 条件2: 放宽Y坐标
            {
                name: '放宽Y坐标',
                filter: (item) => item.y_position > 750 && 
                                 item.width > 30 && item.width < 80 && 
                                 item.height > 20 && item.height < 50
            },
            // 条件3: 放宽尺寸条件
            {
                name: '放宽尺寸',
                filter: (item) => item.y_position > 800 && 
                                 item.width > 25 && item.width < 100 && 
                                 item.height > 15 && item.height < 60
            },
            // 条件4: 最宽松条件
            {
                name: '最宽松条件',
                filter: (item) => item.y_position > 750 && 
                                 item.width > 20 && item.width < 120 && 
                                 item.height > 10 && item.height < 70
            }
        ];
        
        let selectedElements = [];
        let usedCondition = '';
        
        // 尝试每个条件，直到找到合适数量的元素
        for (const condition of filterConditions) {
            const filtered = bottomElements.filter(condition.filter);
            
            console.log(`🧪 ${condition.name}: 找到${filtered.length}个候选元素`);
            
            if (filtered.length >= 6 && filtered.length <= 24) { // 合理的时间轴元素数量
                selectedElements = filtered;
                usedCondition = condition.name;
                break;
            }
        }
        
        if (selectedElements.length === 0) {
            console.log('⚠️ 未找到合适的时间轴元素，使用最宽松条件的前24个');
            const allFiltered = bottomElements.filter(filterConditions[3].filter);
            selectedElements = allFiltered.slice(0, 24);
            usedCondition = '强制选择';
        }
        
        // 按x坐标排序 - 精确模拟Python: timeline_elements.sort(key=lambda x: x['x_position'])
        selectedElements.sort((a, b) => a.x_position - b.x_position);
        
        // 转换为Python脚本兼容的格式
        for (const item of selectedElements) {
            timelineElements.push({
                element: item.element,
                x_position: item.x_position
            });
        }
        
        console.log(`✅ 使用${usedCondition}找到${timelineElements.length}个时间轴元素`);
        
        // 打印排序后的时间轴元素
        console.log('📋 时间轴元素列表（按x坐标排序）:');
        timelineElements.forEach((item, index) => {
            const text = item.element.textContent || '';
            console.log(`  ${index}: x=${Math.round(item.x_position)}, text="${text}"`);
        });
        
        return timelineElements;
    }

    // 精确模拟Python脚本的时间轴切换逻辑
    async getDataForAllHours(dataType) {
        console.log(`📊 获取${dataType}的所有时间点数据`);
        
        const timelineElements = await this.findTimelineElements();
        if (!timelineElements.length) {
            console.log('❌ 未找到时间轴元素');
            return {};
        }
        
        const totalElements = timelineElements.length;
        const dataResults = {};
        
        console.log(`📊 开始遍历${this.targetHours.length}个目标时间点`);
        console.log(`📊 时间轴总元素数: ${totalElements}`);
        
        for (const targetHour of this.targetHours) {
            try {
                console.log(`🕐 处理${targetHour}点数据...`);
                
                // 精确模拟Python脚本的时间轴切换逻辑：
                // hours_per_element = 24 / total_elements
                // element_index = int(target_hour / hours_per_element)
                // element_index = max(0, min(element_index, total_elements - 1))
                const hoursPerElement = 24 / totalElements;
                let elementIndex = Math.floor(targetHour / hoursPerElement);
                elementIndex = Math.max(0, Math.min(elementIndex, totalElements - 1));
                
                const element = timelineElements[elementIndex].element;
                
                console.log(`🎯 点击时间轴元素 ${elementIndex}/${totalElements-1}:`, {
                    targetHour: targetHour,
                    hoursPerElement: hoursPerElement.toFixed(2),
                    calculatedIndex: elementIndex,
                    x_position: Math.round(timelineElements[elementIndex].x_position),
                    text: element.textContent || ''
                });
                
                // 精确模拟Python脚本的点击逻辑
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await this.sleep(1000);
                
                // 使用ActionChains等效的操作
                const event = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true
                });
                element.dispatchEvent(event);
                
                await this.sleep(3000); // 与Python脚本相同的等待时间
                
                // 悬停获取数据
                await this.hoverMapCenter();
                
                // 提取数据
                let hourData = null;
                if (dataType === "温度") {
                    hourData = this.extractTemperatureData();
                } else if (dataType === "降水量") {
                    hourData = this.extractPrecipitationData();
                } else if (dataType === "云量") {
                    hourData = this.extractCloudcoverData();
                } else if (dataType === "风速") {
                    hourData = this.extractWindspeedData();
                }
                
                if (hourData) {
                    const timeKey = `${targetHour.toString().padStart(2, '0')}:00`;
                    dataResults[timeKey] = hourData;
                    console.log(`✅ ${timeKey} ${dataType}数据:`, hourData);
                } else {
                    console.log(`⚠️ ${targetHour}点${dataType}数据提取失败`);
                }
                
            } catch (error) {
                console.error(`❌ 获取${targetHour}点数据失败:`, error);
                continue;
            }
        }
        
        console.log(`📊 ${dataType}数据提取完成，成功获取${Object.keys(dataResults).length}/${this.targetHours.length}个时间点:`, dataResults);
        return dataResults;
    }

    async hoverMapCenter() {
        console.log('🎯 悬停在地图中心');
        
        try {
            const canvas = document.querySelector('canvas');
            if (!canvas) {
                console.log('❌ 未找到canvas元素');
                return;
            }
            
            const rect = canvas.getBoundingClientRect();
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            // 创建鼠标悬停事件
            const event = new MouseEvent('mouseover', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + centerX,
                clientY: rect.top + centerY
            });
            
            canvas.dispatchEvent(event);
            await this.sleep(3000); // 与Python脚本相同的等待时间
            
        } catch (error) {
            console.error('❌ 悬停失败:', error);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 初始化
const extractorV2 = new VentuskyWeatherExtractorV2();

    // 精确模拟Python脚本的风速切换逻辑
    async switchToWindspeed() {
        console.log('💨 开始精确风速切换（模拟Python脚本）...');

        try {
            // 第一步：点击左侧菜单中的"风速"选项
            const windspeedClicked = await this.clickWindspeedInLeftMenu();
            if (!windspeedClicked) {
                console.log('❌ 风速选项点击失败');
                return false;
            }

            // 第二步：精确点击高度下拉框并选择地上100米
            const height100mSelected = await this.clickHeightDropdownAndSelect100mPrecise();

            if (height100mSelected) {
                console.log('✅ 成功切换到风速(100米)图层');
                return true;
            } else {
                console.log('⚠️ 未能选择100米高度，但风速图层已切换');
                return true; // 即使没有选择100米，风速图层也已切换
            }

        } catch (error) {
            console.error('❌ 精确风速切换失败:', error);
            return false;
        }
    }

    // 精确模拟Python脚本：点击左侧菜单中的"风速"选项
    async clickWindspeedInLeftMenu() {
        console.log('💨 点击左侧菜单中的"风速"选项...');

        try {
            // 查找包含"风速"文本的元素
            const windspeedElements = document.querySelectorAll('*');

            for (const element of windspeedElements) {
                try {
                    const text = element.textContent || '';
                    if (text.includes('风速') && element.offsetParent !== null) {
                        console.log(`找到风速选项: ${text}`);

                        // 滚动到元素可见
                        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        await this.sleep(1000);

                        // 点击风速选项
                        element.click();

                        console.log('✅ 成功点击左侧菜单中的"风速"选项');
                        await this.sleep(5000); // 等待图层切换

                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }

            console.log('❌ 未找到左侧菜单中的风速选项');
            return false;

        } catch (error) {
            console.log(`❌ 点击左侧风速选项失败: ${error}`);
            return false;
        }
    }

    // 精确模拟Python脚本：点击高度下拉框并选择地上100米
    async clickHeightDropdownAndSelect100mPrecise() {
        console.log('🏔️ 精确操作：点击"地上10米"下拉框并选择"地上100米"...');

        try {
            // 第一步：找到并点击"地上10米"下拉框
            console.log('🔍 查找"地上10米"下拉框...');

            let dropdownClicked = false;

            // 尝试多种方法找到下拉框（模拟Python的多策略方法）
            const dropdownStrategies = [
                // 策略1：直接查找包含"地上10米"的元素
                () => this.findElementsByText('地上10米'),
                // 策略2：查找包含"10米"的可点击元素
                () => this.findElementsByText('10米'),
                // 策略3：查找高度相关的元素
                () => this.findElementsByText('高度')
            ];

            for (let strategyNum = 0; strategyNum < dropdownStrategies.length; strategyNum++) {
                if (dropdownClicked) break;

                try {
                    console.log(`尝试策略 ${strategyNum + 1}...`);
                    const elements = dropdownStrategies[strategyNum]();

                    for (const element of elements) {
                        try {
                            if (element.offsetParent !== null) {
                                const text = element.textContent || '';
                                console.log(`找到候选下拉框: "${text}" - 标签: ${element.tagName}`);

                                // 滚动到元素可见
                                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                await this.sleep(1000);

                                // 尝试多种点击方法
                                const clickMethods = [
                                    // 方法1：普通点击
                                    () => element.click(),
                                    // 方法2：鼠标事件
                                    () => {
                                        const event = new MouseEvent('click', {
                                            view: window,
                                            bubbles: true,
                                            cancelable: true
                                        });
                                        element.dispatchEvent(event);
                                    }
                                ];

                                for (let methodNum = 0; methodNum < clickMethods.length; methodNum++) {
                                    try {
                                        console.log(`  尝试点击方法 ${methodNum + 1}...`);
                                        clickMethods[methodNum]();
                                        await this.sleep(3000); // 等待下拉选项出现

                                        // 检查是否成功打开下拉框
                                        if (await this.checkDropdownOpened()) {
                                            console.log('✅ 成功打开高度下拉框');
                                            dropdownClicked = true;
                                            break;
                                        }
                                    } catch (error) {
                                        console.log(`    点击方法 ${methodNum + 1} 失败: ${error.message}`);
                                        continue;
                                    }
                                }

                                if (dropdownClicked) break;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                } catch (error) {
                    console.log(`策略 ${strategyNum + 1} 失败: ${error}`);
                    continue;
                }
            }

            if (!dropdownClicked) {
                console.log('❌ 未能打开高度下拉框');
                return false;
            }

            // 第二步：在弹出的选择栏中精确选择"地上100米"
            console.log('🔍 在下拉选项中精确查找并点击"地上100米"...');

            // 等待下拉选项完全加载
            await this.sleep(2000);

            // 查找100米选项的多种策略（模拟Python的多策略方法）
            const optionStrategies = [
                // 策略1：直接查找"地上100米"
                () => this.findElementsByText('地上100米'),
                // 策略2：查找"100米"
                () => this.findElementsByText('100米'),
                // 策略3：查找"100m"
                () => this.findElementsByText('100m'),
                // 策略4：查找包含100的元素
                () => this.findElementsByText('100')
            ];

            let optionClicked = false;

            for (let strategyNum = 0; strategyNum < optionStrategies.length; strategyNum++) {
                if (optionClicked) break;

                try {
                    console.log(`尝试选项策略 ${strategyNum + 1}...`);
                    const elements = optionStrategies[strategyNum]();

                    // 过滤出最可能的100米选项
                    const validElements = [];
                    for (const element of elements) {
                        try {
                            if (element.offsetParent !== null) {
                                const text = element.textContent?.trim() || '';
                                console.log(`  发现选项: "${text}" - 标签: ${element.tagName}`);

                                // 优先选择包含"地上100米"的选项
                                if (text.includes('地上100米')) {
                                    validElements.unshift(element); // 插入到最前面
                                } else if (text.includes('100米') || text.includes('100m')) {
                                    validElements.push(element);
                                }
                            }
                        } catch {
                            continue;
                        }
                    }

                    // 尝试点击最合适的选项
                    for (const element of validElements) {
                        try {
                            const text = element.textContent || '';
                            console.log(`  尝试点击选项: "${text}"`);

                            // 尝试多种点击方法
                            const clickMethods = [
                                () => element.click(),
                                () => {
                                    const event = new MouseEvent('click', {
                                        view: window,
                                        bubbles: true,
                                        cancelable: true
                                    });
                                    element.dispatchEvent(event);
                                }
                            ];

                            for (const clickMethod of clickMethods) {
                                try {
                                    clickMethod();
                                    await this.sleep(3000); // 等待选项应用

                                    // 验证是否成功选择了100米
                                    if (await this.verify100mSelected()) {
                                        console.log('✅ 成功选择"地上100米"选项');
                                        optionClicked = true;
                                        break;
                                    }
                                } catch (error) {
                                    continue;
                                }
                            }

                            if (optionClicked) break;
                        } catch (error) {
                            continue;
                        }
                    }
                } catch (error) {
                    console.log(`选项策略 ${strategyNum + 1} 失败: ${error}`);
                    continue;
                }
            }

            return optionClicked;

        } catch (error) {
            console.log(`❌ 精确选择地上100米失败: ${error}`);
            return false;
        }
    }

    // 辅助方法：根据文本查找元素
    findElementsByText(searchText) {
        const elements = [];
        const allElements = document.querySelectorAll('*');

        for (const element of allElements) {
            const text = element.textContent || '';
            if (text.includes(searchText)) {
                elements.push(element);
            }
        }

        return elements;
    }

    // 检查下拉框是否已打开
    async checkDropdownOpened() {
        try {
            // 查找可能的下拉选项
            const dropdownOptions = this.findElementsByText('100');
            return dropdownOptions.length > 0;
        } catch {
            return false;
        }
    }

    // 验证是否成功选择了100米
    async verify100mSelected() {
        try {
            // 查找页面上是否显示"地上100米"
            const currentSelection = this.findElementsByText('地上100米');

            for (const element of currentSelection) {
                if (element.offsetParent !== null) {
                    const text = element.textContent || '';
                    console.log(`当前高度显示: "${text}"`);
                    if (text.includes('100')) {
                        return true;
                    }
                }
            }

            // 也检查是否有"100米"显示
            const selection100m = this.findElementsByText('100米');
            for (const element of selection100m) {
                if (element.offsetParent !== null) {
                    const text = element.textContent || '';
                    console.log(`当前高度显示: "${text}"`);
                    if (text.includes('100')) {
                        return true;
                    }
                }
            }

            return false;
        } catch {
            return false;
        }
    }

    // 完整的天气数据提取流程
    async startExtraction(config) {
        console.log('🌤️ 开始天气数据提取 (完全修复版)', config);
        
        this.isExtracting = true;
        this.config = config;
        this.allWeatherData = {};
        
        try {
            this.showOverlay('正在初始化...');
            
            // 等待页面完全加载
            await this.sleep(5000);
            
            // 获取温度数据（默认图层）
            console.log('\n🌡️ 获取温度数据...');
            this.updateOverlay('获取温度数据...', 20);
            const tempData = await this.getDataForAllHours("温度");
            
            // 切换到降水量并获取数据
            console.log('\n🌧️ 获取降水量数据...');
            this.updateOverlay('获取降水量数据...', 40);
            await this.switchToDataType("降水量");
            const precipData = await this.getDataForAllHours("降水量");
            
            // 切换到云量并获取数据
            console.log('\n☁️ 获取云量数据...');
            this.updateOverlay('获取云量数据...', 60);
            await this.switchToDataType("云量");
            const cloudData = await this.getDataForAllHours("云量");
            
            // 切换到风速并获取数据
            console.log('\n💨 获取风速数据...');
            this.updateOverlay('获取风速数据...', 80);
            await this.switchToDataType("风速");
            const windData = await this.getDataForAllHours("风速");
            
            // 整合数据
            this.updateOverlay('整合数据...', 90);
            const finalResult = this.integrateWeatherData(tempData, precipData, cloudData, windData);
            
            this.updateOverlay('提取完成！', 100);
            
            console.log('🎉 天气数据提取完成:', finalResult);
            
            // 发送数据到popup
            chrome.runtime.sendMessage({
                action: 'extractionComplete',
                data: finalResult
            });
            
            this.hideOverlay();
            this.isExtracting = false;
            
            return { success: true, data: finalResult };
            
        } catch (error) {
            console.error('❌ 天气数据提取失败:', error);
            this.hideOverlay();
            this.isExtracting = false;
            
            return { success: false, error: error.message };
        }
    }

    integrateWeatherData(tempData, precipData, cloudData, windData) {
        const integratedData = {};
        
        for (const hour of this.targetHours) {
            const timeKey = `${hour.toString().padStart(2, '0')}:00`;
            
            integratedData[timeKey] = {
                temperature: tempData[timeKey]?.temperature || 'N/A',
                precipitation: precipData[timeKey]?.precipitation || '0mm',
                cloudcover: cloudData[timeKey]?.cloudcover || '0%',
                windspeed: windData[timeKey]?.windspeed || '0km/h'
            };
        }
        
        return {
            location: this.getCurrentCoordinates(),
            date: new Date().toISOString().split('T')[0],
            data: integratedData,
            extractedAt: new Date().toISOString()
        };
    }

    showOverlay(message) {
        this.hideOverlay(); // 确保没有重复的overlay
        
        const overlay = document.createElement('div');
        overlay.id = 'weather-extractor-overlay';
        overlay.className = 'weather-extractor-overlay';
        overlay.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px;">🌤️ Ventusky Weather Extractor V2</div>
            <div id="weather-extractor-message">${message}</div>
            <div class="weather-extractor-progress">
                <div id="weather-extractor-progress-bar" class="weather-extractor-progress-bar" style="width: 0%"></div>
            </div>
        `;
        
        document.body.appendChild(overlay);
    }

    updateOverlay(message, progress) {
        const messageEl = document.getElementById('weather-extractor-message');
        const progressBar = document.getElementById('weather-extractor-progress-bar');
        
        if (messageEl) messageEl.textContent = message;
        if (progressBar) progressBar.style.width = progress + '%';
    }

    hideOverlay() {
        const overlay = document.getElementById('weather-extractor-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    stopExtraction() {
        this.isExtracting = false;
        this.hideOverlay();
        console.log('⏹️ 天气数据提取已停止');
    }
}

// 初始化完全修复版
const extractorV2 = new VentuskyWeatherExtractorV2();

// 添加全局调试函数
window.ventuskyDebug = {
    findTimelineElements: () => extractorV2.findTimelineElements(),
    switchToWindspeed: () => extractorV2.switchToWindspeed(),
    clickWindspeedInLeftMenu: () => extractorV2.clickWindspeedInLeftMenu(),
    clickHeightDropdownAndSelect100m: () => extractorV2.clickHeightDropdownAndSelect100mPrecise(),
    verify100mSelected: () => extractorV2.verify100mSelected(),
    testExtraction: () => extractorV2.startExtraction({ lat: 30.183, lon: 120.2 })
};

console.log('💡 调试提示: 可以在控制台调用以下函数:');
console.log('  - window.ventuskyDebug.findTimelineElements() // 测试时间轴查找');
console.log('  - window.ventuskyDebug.switchToWindspeed() // 测试完整风速切换');
console.log('  - window.ventuskyDebug.clickWindspeedInLeftMenu() // 测试风速菜单点击');
console.log('  - window.ventuskyDebug.clickHeightDropdownAndSelect100m() // 测试100米选择');
console.log('  - window.ventuskyDebug.verify100mSelected() // 验证100米是否选中');
console.log('  - window.ventuskyDebug.testExtraction() // 测试完整提取流程');
