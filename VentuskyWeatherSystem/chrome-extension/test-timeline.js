// 时间轴修复测试脚本
// 在浏览器控制台中运行此脚本来测试时间轴功能

console.log('🧪 开始时间轴修复测试...');

// 测试时间轴元素查找
async function testTimelineElements() {
    console.log('\n📋 测试1: 时间轴元素查找');
    
    const timelineElements = [];
    const allLinks = document.querySelectorAll('a');
    
    console.log(`🔍 检查${allLinks.length}个链接元素`);
    
    for (const link of allLinks) {
        try {
            if (link.offsetParent !== null) {
                const rect = link.getBoundingClientRect();
                const location = { x: rect.left, y: rect.top };
                const size = { width: rect.width, height: rect.height };
                
                // 使用修复后的条件
                if (location.y > 800 && 
                    size.width > 30 && size.width < 80 && 
                    size.height > 20 && size.height < 50) {
                    
                    timelineElements.push({
                        element: link,
                        x_position: location.x,
                        text: link.textContent || '',
                        y: location.y,
                        width: size.width,
                        height: size.height
                    });
                }
            }
        } catch {
            continue;
        }
    }
    
    // 按x坐标排序
    timelineElements.sort((a, b) => a.x_position - b.x_position);
    
    console.log(`✅ 找到${timelineElements.length}个时间轴元素:`);
    timelineElements.forEach((item, index) => {
        console.log(`  ${index}: x=${Math.round(item.x_position)}, y=${Math.round(item.y)}, w=${Math.round(item.width)}, h=${Math.round(item.height)}, text="${item.text}"`);
    });
    
    return timelineElements;
}

// 测试时间轴索引计算
function testTimelineCalculation(timelineElements) {
    console.log('\n🧮 测试2: 时间轴索引计算');
    
    const targetHours = [2, 5, 8, 11, 17, 23];
    const totalElements = timelineElements.length;
    
    console.log(`📊 总时间轴元素数: ${totalElements}`);
    console.log(`🎯 目标时间点: ${targetHours.join(', ')}`);
    
    const calculations = [];
    
    for (const targetHour of targetHours) {
        const hoursPerElement = 24 / totalElements;
        let elementIndex = Math.floor(targetHour / hoursPerElement);
        elementIndex = Math.max(0, Math.min(elementIndex, totalElements - 1));
        
        const calculation = {
            targetHour,
            hoursPerElement: hoursPerElement.toFixed(2),
            calculatedIndex: elementIndex,
            elementText: timelineElements[elementIndex]?.text || 'N/A',
            elementX: Math.round(timelineElements[elementIndex]?.x_position || 0)
        };
        
        calculations.push(calculation);
        
        console.log(`🕐 ${targetHour}点 -> 索引${elementIndex} (每元素${hoursPerElement.toFixed(2)}小时) -> "${calculation.elementText}"`);
    }
    
    return calculations;
}

// 测试时间轴点击（模拟）
async function testTimelineClicking(timelineElements, calculations) {
    console.log('\n🖱️ 测试3: 时间轴点击模拟');
    
    for (const calc of calculations) {
        const element = timelineElements[calc.calculatedIndex]?.element;
        
        if (element) {
            console.log(`🎯 模拟点击${calc.targetHour}点 (索引${calc.calculatedIndex}): "${calc.elementText}"`);
            
            // 模拟滚动到元素
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // 高亮元素（测试用）
            const originalStyle = element.style.cssText;
            element.style.cssText += 'border: 3px solid red !important; background: yellow !important;';
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 恢复原样式
            element.style.cssText = originalStyle;
            
            console.log(`✅ ${calc.targetHour}点元素已高亮显示`);
        } else {
            console.log(`❌ ${calc.targetHour}点对应的元素不存在`);
        }
    }
}

// 对比Python脚本逻辑
function comparePythonLogic(timelineElements) {
    console.log('\n🐍 测试4: 与Python脚本逻辑对比');
    
    const targetHours = [2, 5, 8, 11, 17, 23];
    const totalElements = timelineElements.length;
    
    console.log('Python脚本逻辑:');
    console.log('  hours_per_element = 24 / total_elements');
    console.log('  element_index = int(target_hour / hours_per_element)');
    console.log('  element_index = max(0, min(element_index, total_elements - 1))');
    
    console.log('\nJavaScript修复逻辑:');
    console.log('  const hoursPerElement = 24 / totalElements;');
    console.log('  let elementIndex = Math.floor(targetHour / hoursPerElement);');
    console.log('  elementIndex = Math.max(0, Math.min(elementIndex, totalElements - 1));');
    
    console.log('\n📊 逻辑对比结果:');
    
    for (const targetHour of targetHours) {
        // Python逻辑
        const pythonHoursPerElement = 24 / totalElements;
        const pythonIndex = Math.max(0, Math.min(Math.floor(targetHour / pythonHoursPerElement), totalElements - 1));
        
        // JavaScript逻辑
        const jsHoursPerElement = 24 / totalElements;
        const jsIndex = Math.max(0, Math.min(Math.floor(targetHour / jsHoursPerElement), totalElements - 1));
        
        const match = pythonIndex === jsIndex ? '✅' : '❌';
        
        console.log(`  ${targetHour}点: Python索引${pythonIndex} vs JS索引${jsIndex} ${match}`);
    }
}

// 运行所有测试
async function runAllTests() {
    try {
        console.log('🚀 开始完整测试流程...\n');
        
        // 等待页面稳定
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 测试1: 时间轴元素查找
        const timelineElements = await testTimelineElements();
        
        if (timelineElements.length === 0) {
            console.log('❌ 未找到时间轴元素，测试终止');
            return;
        }
        
        // 测试2: 时间轴索引计算
        const calculations = testTimelineCalculation(timelineElements);
        
        // 测试3: 时间轴点击模拟
        await testTimelineClicking(timelineElements, calculations);
        
        // 测试4: 与Python脚本逻辑对比
        comparePythonLogic(timelineElements);
        
        console.log('\n🎉 所有测试完成！');
        console.log('📋 测试总结:');
        console.log(`  - 找到${timelineElements.length}个时间轴元素`);
        console.log(`  - 计算了${calculations.length}个时间点的索引`);
        console.log(`  - 模拟点击了${calculations.length}个元素`);
        console.log(`  - 验证了与Python脚本的逻辑一致性`);
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    }
}

// 自动运行测试
console.log('⏳ 3秒后开始测试...');
setTimeout(runAllTests, 3000);

// 导出测试函数供手动调用
window.timelineTest = {
    runAllTests,
    testTimelineElements,
    testTimelineCalculation,
    testTimelineClicking,
    comparePythonLogic
};

console.log('💡 提示: 可以手动调用 window.timelineTest.runAllTests() 重新运行测试');
