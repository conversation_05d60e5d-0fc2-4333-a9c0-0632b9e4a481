# 🔧 Chrome插件问题排查指南

## 🚨 常见错误及解决方案

### 1. Service Worker Registration Failed (Status code: 15)

**问题原因**: background.js文件有语法错误或Chrome版本不兼容

**解决方案**:
```bash
# 1. 检查Chrome版本（需要88+）
# 2. 重新加载插件
# 3. 查看Chrome扩展页面的错误信息
```

**已修复**: 简化了background.js，移除了复杂的类结构

### 2. Cannot read properties of undefined (reading 'create')

**问题原因**: Chrome API调用错误

**解决方案**:
- 检查manifest.json中的权限配置
- 确保在正确的上下文中调用API

**已修复**: 修正了Chrome API的调用方式

### 3. Could not establish connection. Receiving end does not exist

**问题原因**: content script和popup之间通信失败

**解决方案**:
1. 确保在Ventusky页面使用插件
2. 刷新页面后重试
3. 检查content script是否正确加载

**已修复**: 添加了错误处理和重试机制

### 4. 提取逻辑不正确

**问题原因**: JavaScript版本没有准确模拟Python脚本的逻辑

**已修复的问题**:
- ✅ 时间轴元素识别逻辑
- ✅ 数据提取正则表达式
- ✅ 图层切换逻辑
- ✅ 键盘导航（右箭头键）
- ✅ 鼠标悬停事件

## 🧪 测试步骤

### 第一步：安装插件
1. 打开 `chrome://extensions/`
2. 开启"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择 `chrome-extension` 文件夹

### 第二步：验证安装
1. 插件应该出现在扩展列表中
2. 浏览器工具栏显示插件图标
3. 控制台无错误信息

### 第三步：功能测试
1. 访问 https://www.ventusky.com
2. 点击插件图标打开弹窗
3. 配置提取参数：
   - 坐标：30.1833, 120.2
   - 日期：选择1-2天测试
   - 时间点：选择2-3个时间点
   - 数据类型：选择温度和降水量

4. 点击"开始提取"
5. 观察页面操作和控制台日志

### 第四步：调试信息
打开Chrome开发者工具查看：
- **Console**: 查看详细的执行日志
- **Network**: 检查网络请求
- **Elements**: 观察DOM变化

## 🔍 调试技巧

### 查看详细日志
```javascript
// 在控制台执行，查看插件状态
console.log('插件状态检查');
chrome.runtime.getManifest();
```

### 手动测试数据提取
```javascript
// 在Ventusky页面控制台执行
// 测试时间轴元素查找
const links = document.querySelectorAll('a');
const timelineElements = [];
for (const link of links) {
    const rect = link.getBoundingClientRect();
    if (rect.top > window.innerHeight * 0.75 && 
        rect.width > 25 && rect.width < 100) {
        timelineElements.push({
            element: link,
            text: link.textContent,
            rect: rect
        });
    }
}
console.log('找到的时间轴元素:', timelineElements);
```

### 测试数据提取
```javascript
// 测试温度数据提取
const allElements = document.querySelectorAll('*');
for (const element of allElements) {
    if (element.offsetParent !== null) {
        const text = element.textContent || '';
        const tempMatch = text.match(/(-?\d+)\s*°C?/);
        if (tempMatch) {
            const tempValue = parseInt(tempMatch[1]);
            if (tempValue >= -50 && tempValue <= 60) {
                console.log('找到温度:', tempValue + '°C', element);
                break;
            }
        }
    }
}
```

## 📋 已知限制

1. **页面加载时间**: 需要等待Ventusky完全加载
2. **网络延迟**: 可能影响数据提取准确性
3. **页面结构变化**: Ventusky更新可能影响元素识别
4. **浏览器兼容性**: 仅支持Chrome 88+

## 🔄 如果仍有问题

1. **重新安装插件**:
   - 删除现有插件
   - 清除浏览器缓存
   - 重新加载插件

2. **检查权限**:
   - 确保插件有访问Ventusky的权限
   - 检查是否被广告拦截器阻止

3. **更新Chrome**:
   - 确保使用最新版本的Chrome
   - 重启浏览器

4. **查看错误日志**:
   - Chrome扩展页面的错误信息
   - 开发者工具的控制台日志
   - 网络请求状态

## 📞 获取帮助

如果问题仍然存在，请提供：
1. Chrome版本号
2. 错误截图
3. 控制台日志
4. 具体的操作步骤

---

**最新更新**: 2025-08-04 - 修复了主要的通信和数据提取问题
