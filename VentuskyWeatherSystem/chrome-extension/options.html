<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ventusky Weather Extractor - 设置</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="container">
        <!-- 标题栏 -->
        <header class="header">
            <div class="logo">
                <span class="weather-icon">🌤️</span>
                <h1>天气数据提取器 - 设置</h1>
            </div>
            <div class="version" id="version">v1.0.0</div>
        </header>

        <!-- 导航标签 -->
        <nav class="tabs">
            <button class="tab-btn active" data-tab="general">常规设置</button>
            <button class="tab-btn" data-tab="extraction">提取设置</button>
            <button class="tab-btn" data-tab="export">导出设置</button>
            <button class="tab-btn" data-tab="advanced">高级设置</button>
            <button class="tab-btn" data-tab="about">关于</button>
        </nav>

        <!-- 设置内容 -->
        <main class="content">
            <!-- 常规设置 -->
            <div class="tab-content active" id="general">
                <div class="section">
                    <h2>📍 默认位置</h2>
                    <p class="description">设置插件启动时的默认坐标位置</p>
                    
                    <div class="form-group">
                        <label for="defaultLat">默认纬度:</label>
                        <input type="number" id="defaultLat" step="0.0001" min="-90" max="90" value="30.1833">
                        <span class="help-text">范围: -90 到 90</span>
                    </div>
                    
                    <div class="form-group">
                        <label for="defaultLon">默认经度:</label>
                        <input type="number" id="defaultLon" step="0.0001" min="-180" max="180" value="120.2">
                        <span class="help-text">范围: -180 到 180</span>
                    </div>
                    
                    <div class="preset-locations">
                        <h3>常用位置</h3>
                        <div class="location-buttons">
                            <button class="location-btn" data-lat="39.9042" data-lon="116.4074">北京</button>
                            <button class="location-btn" data-lat="31.2304" data-lon="121.4737">上海</button>
                            <button class="location-btn" data-lat="30.1833" data-lon="120.2">杭州</button>
                            <button class="location-btn" data-lat="23.1291" data-lon="113.2644">广州</button>
                            <button class="location-btn" data-lat="30.5728" data-lon="104.0668">成都</button>
                            <button class="location-btn" data-lat="29.5630" data-lon="106.5516">重庆</button>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2>🎨 界面设置</h2>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="showNotifications" checked>
                            <span class="checkmark"></span>
                            显示通知消息
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoSave" checked>
                            <span class="checkmark"></span>
                            自动保存提取结果
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label for="theme">界面主题:</label>
                        <select id="theme">
                            <option value="auto">跟随系统</option>
                            <option value="light">浅色主题</option>
                            <option value="dark">深色主题</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 提取设置 -->
            <div class="tab-content" id="extraction">
                <div class="section">
                    <h2>⏱️ 时间设置</h2>
                    
                    <div class="form-group">
                        <label for="extractionTimeout">提取超时时间 (秒):</label>
                        <input type="number" id="extractionTimeout" min="60" max="600" value="300">
                        <span class="help-text">单次提取的最大等待时间</span>
                    </div>
                    
                    <div class="form-group">
                        <label for="delayBetweenRequests">请求间隔 (毫秒):</label>
                        <input type="number" id="delayBetweenRequests" min="1000" max="10000" value="2000">
                        <span class="help-text">避免请求过于频繁</span>
                    </div>
                    
                    <div class="form-group">
                        <label for="retryAttempts">重试次数:</label>
                        <input type="number" id="retryAttempts" min="1" max="5" value="3">
                        <span class="help-text">失败时的重试次数</span>
                    </div>
                </div>

                <div class="section">
                    <h2>📊 数据设置</h2>
                    
                    <div class="form-group">
                        <label for="maxDays">最大提取天数:</label>
                        <input type="number" id="maxDays" min="1" max="30" value="14">
                        <span class="help-text">单次提取的最大天数限制</span>
                    </div>
                    
                    <div class="form-group">
                        <h3>默认时间点</h3>
                        <div class="time-checkboxes">
                            <label class="checkbox-label">
                                <input type="checkbox" class="default-hour" value="2" checked>
                                <span class="checkmark"></span>
                                02:00
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" class="default-hour" value="5" checked>
                                <span class="checkmark"></span>
                                05:00
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" class="default-hour" value="8" checked>
                                <span class="checkmark"></span>
                                08:00
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" class="default-hour" value="11" checked>
                                <span class="checkmark"></span>
                                11:00
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" class="default-hour" value="17" checked>
                                <span class="checkmark"></span>
                                17:00
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" class="default-hour" value="23" checked>
                                <span class="checkmark"></span>
                                23:00
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <h3>默认数据类型</h3>
                        <div class="data-checkboxes">
                            <label class="checkbox-label">
                                <input type="checkbox" class="default-data" value="temperature" checked>
                                <span class="checkmark"></span>
                                🌡️ 温度
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" class="default-data" value="precipitation" checked>
                                <span class="checkmark"></span>
                                🌧️ 降水量
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" class="default-data" value="cloudcover" checked>
                                <span class="checkmark"></span>
                                ☁️ 云量
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" class="default-data" value="windspeed" checked>
                                <span class="checkmark"></span>
                                💨 风速
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 导出设置 -->
            <div class="tab-content" id="export">
                <div class="section">
                    <h2>📁 文件设置</h2>
                    
                    <div class="form-group">
                        <label for="defaultExportFormat">默认导出格式:</label>
                        <select id="defaultExportFormat">
                            <option value="json">JSON</option>
                            <option value="csv">CSV</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="filenameTemplate">文件名模板:</label>
                        <input type="text" id="filenameTemplate" value="ventusky_weather_{date}">
                        <span class="help-text">可用变量: {date}, {time}, {location}</span>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="includeMetadata" checked>
                            <span class="checkmark"></span>
                            包含元数据信息
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="compressFiles">
                            <span class="checkmark"></span>
                            压缩大文件
                        </label>
                    </div>
                </div>

                <div class="section">
                    <h2>📊 历史记录</h2>
                    
                    <div class="form-group">
                        <label for="maxHistoryItems">最大历史记录数:</label>
                        <input type="number" id="maxHistoryItems" min="10" max="200" value="50">
                        <span class="help-text">超出限制时自动删除最旧的记录</span>
                    </div>
                    
                    <div class="form-group">
                        <label for="historyRetentionDays">历史记录保留天数:</label>
                        <input type="number" id="historyRetentionDays" min="7" max="365" value="30">
                        <span class="help-text">自动清理过期的历史记录</span>
                    </div>
                    
                    <div class="form-group">
                        <button class="btn-secondary" id="clearHistoryBtn">清空历史记录</button>
                        <button class="btn-secondary" id="exportHistoryBtn">导出历史记录</button>
                    </div>
                </div>
            </div>

            <!-- 高级设置 -->
            <div class="tab-content" id="advanced">
                <div class="section">
                    <h2>🔧 调试设置</h2>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableDebugMode">
                            <span class="checkmark"></span>
                            启用调试模式
                        </label>
                        <span class="help-text">在控制台显示详细日志</span>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enablePerformanceMonitoring">
                            <span class="checkmark"></span>
                            启用性能监控
                        </label>
                        <span class="help-text">记录提取性能数据</span>
                    </div>
                </div>

                <div class="section">
                    <h2>🔄 数据管理</h2>
                    
                    <div class="form-group">
                        <button class="btn-secondary" id="resetSettingsBtn">重置所有设置</button>
                        <button class="btn-secondary" id="exportSettingsBtn">导出设置</button>
                        <button class="btn-secondary" id="importSettingsBtn">导入设置</button>
                        <input type="file" id="importSettingsFile" accept=".json" style="display: none;">
                    </div>
                    
                    <div class="form-group">
                        <button class="btn-danger" id="clearAllDataBtn">清空所有数据</button>
                        <span class="help-text">⚠️ 此操作将删除所有设置和历史记录</span>
                    </div>
                </div>
            </div>

            <!-- 关于 -->
            <div class="tab-content" id="about">
                <div class="section">
                    <h2>ℹ️ 插件信息</h2>
                    
                    <div class="info-card">
                        <h3>Ventusky Weather Extractor</h3>
                        <p><strong>版本:</strong> <span id="aboutVersion">1.0.0</span></p>
                        <p><strong>作者:</strong> Weather Data Team</p>
                        <p><strong>描述:</strong> 专业级天气数据提取工具，支持多日、多参数、多时间点天气预报获取</p>
                    </div>
                    
                    <div class="info-card">
                        <h3>🌟 主要功能</h3>
                        <ul>
                            <li>✅ 4参数获取：温度、降水量、云量、风速</li>
                            <li>✅ 6时间点：02:00, 05:00, 08:00, 11:00, 17:00, 23:00</li>
                            <li>✅ 多日提取：支持最多14天连续预报</li>
                            <li>✅ 多格式导出：JSON、CSV、Excel</li>
                            <li>✅ 智能分析：自动生成综合天气状况</li>
                            <li>✅ 历史记录：完整的提取历史管理</li>
                        </ul>
                    </div>
                    
                    <div class="info-card">
                        <h3>📊 使用统计</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-value" id="totalExtractions">0</span>
                                <span class="stat-label">总提取次数</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value" id="totalDays">0</span>
                                <span class="stat-label">总提取天数</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value" id="successRate">0%</span>
                                <span class="stat-label">成功率</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value" id="lastUsed">从未</span>
                                <span class="stat-label">最后使用</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <h3>🔗 相关链接</h3>
                        <div class="links">
                            <a href="https://www.ventusky.com" target="_blank">Ventusky 官网</a>
                            <a href="#" id="helpLink">使用帮助</a>
                            <a href="#" id="feedbackLink">问题反馈</a>
                            <a href="#" id="updateLogLink">更新日志</a>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部操作栏 -->
        <footer class="footer">
            <div class="status" id="saveStatus"></div>
            <div class="actions">
                <button class="btn-secondary" id="resetBtn">重置</button>
                <button class="btn-primary" id="saveBtn">保存设置</button>
            </div>
        </footer>
    </div>

    <script src="options.js"></script>
</body>
</html>
