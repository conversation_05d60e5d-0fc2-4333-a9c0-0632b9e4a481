/* Ventusky Weather Extractor - Content Styles */

/* 提取器覆盖层样式 */
.weather-extractor-overlay {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    background: rgba(0, 0, 0, 0.9) !important;
    color: white !important;
    padding: 20px !important;
    border-radius: 12px !important;
    z-index: 999999 !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    font-size: 14px !important;
    max-width: 320px !important;
    min-width: 280px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    animation: slideInRight 0.3s ease-out !important;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.weather-extractor-overlay .title {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    margin-bottom: 12px !important;
    color: #4CAF50 !important;
}

.weather-extractor-overlay .message {
    margin: 8px 0 !important;
    line-height: 1.4 !important;
    color: #e0e0e0 !important;
}

.weather-extractor-overlay .details {
    font-size: 12px !important;
    color: #b0b0b0 !important;
    margin-top: 8px !important;
}

/* 进度条样式 */
.weather-extractor-progress {
    margin-top: 15px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    height: 6px !important;
    border-radius: 3px !important;
    overflow: hidden !important;
    position: relative !important;
}

.weather-extractor-progress-bar {
    background: linear-gradient(90deg, #4CAF50, #45a049) !important;
    height: 100% !important;
    transition: width 0.4s ease !important;
    border-radius: 3px !important;
    position: relative !important;
}

.weather-extractor-progress-bar::after {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent) !important;
    animation: shimmer 2s infinite !important;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.weather-extractor-progress-text {
    margin-top: 8px !important;
    font-size: 12px !important;
    color: #b0b0b0 !important;
    text-align: center !important;
}

/* 状态指示器 */
.weather-extractor-status {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    margin-bottom: 10px !important;
}

.weather-extractor-status-dot {
    width: 8px !important;
    height: 8px !important;
    border-radius: 50% !important;
    background: #4CAF50 !important;
    animation: pulse 2s infinite !important;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

.weather-extractor-status-text {
    font-size: 13px !important;
    color: #e0e0e0 !important;
    font-weight: 500 !important;
}

/* 数据统计 */
.weather-extractor-stats {
    margin-top: 12px !important;
    padding-top: 12px !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.weather-extractor-stat-item {
    display: flex !important;
    justify-content: space-between !important;
    margin-bottom: 4px !important;
    font-size: 12px !important;
}

.weather-extractor-stat-label {
    color: #b0b0b0 !important;
}

.weather-extractor-stat-value {
    color: #4CAF50 !important;
    font-weight: 500 !important;
}

/* 错误状态 */
.weather-extractor-overlay.error {
    border-color: #f44336 !important;
}

.weather-extractor-overlay.error .title {
    color: #f44336 !important;
}

.weather-extractor-overlay.error .weather-extractor-status-dot {
    background: #f44336 !important;
}

.weather-extractor-overlay.error .weather-extractor-progress-bar {
    background: linear-gradient(90deg, #f44336, #d32f2f) !important;
}

/* 成功状态 */
.weather-extractor-overlay.success {
    border-color: #4CAF50 !important;
}

.weather-extractor-overlay.success .title {
    color: #4CAF50 !important;
}

.weather-extractor-overlay.success .weather-extractor-status-dot {
    background: #4CAF50 !important;
    animation: none !important;
}

/* 关闭按钮 */
.weather-extractor-close {
    position: absolute !important;
    top: 8px !important;
    right: 8px !important;
    background: none !important;
    border: none !important;
    color: #b0b0b0 !important;
    font-size: 18px !important;
    cursor: pointer !important;
    padding: 4px !important;
    border-radius: 4px !important;
    transition: all 0.2s ease !important;
}

.weather-extractor-close:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

/* 操作按钮 */
.weather-extractor-actions {
    margin-top: 15px !important;
    display: flex !important;
    gap: 8px !important;
}

.weather-extractor-btn {
    flex: 1 !important;
    padding: 8px 12px !important;
    border: none !important;
    border-radius: 6px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.weather-extractor-btn-primary {
    background: #4CAF50 !important;
    color: white !important;
}

.weather-extractor-btn-primary:hover {
    background: #45a049 !important;
    transform: translateY(-1px) !important;
}

.weather-extractor-btn-secondary {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #e0e0e0 !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.weather-extractor-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .weather-extractor-overlay {
        left: 10px !important;
        right: 10px !important;
        top: 10px !important;
        max-width: none !important;
        min-width: auto !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .weather-extractor-overlay {
        background: black !important;
        border: 2px solid white !important;
    }
    
    .weather-extractor-progress {
        background: #333 !important;
    }
    
    .weather-extractor-progress-bar {
        background: white !important;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .weather-extractor-overlay {
        animation: none !important;
    }
    
    .weather-extractor-progress-bar {
        transition: none !important;
    }
    
    .weather-extractor-status-dot {
        animation: none !important;
    }
    
    .weather-extractor-progress-bar::after {
        animation: none !important;
    }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .weather-extractor-overlay {
        background: rgba(18, 18, 18, 0.95) !important;
        border-color: rgba(255, 255, 255, 0.15) !important;
    }
}

/* 浅色主题适配 */
@media (prefers-color-scheme: light) {
    .weather-extractor-overlay {
        background: rgba(255, 255, 255, 0.95) !important;
        color: #333 !important;
        border-color: rgba(0, 0, 0, 0.15) !important;
    }
    
    .weather-extractor-overlay .message {
        color: #555 !important;
    }
    
    .weather-extractor-overlay .details {
        color: #777 !important;
    }
    
    .weather-extractor-status-text {
        color: #555 !important;
    }
    
    .weather-extractor-stat-label {
        color: #777 !important;
    }
}
