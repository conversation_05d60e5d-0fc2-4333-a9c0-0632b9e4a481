// Ventusky Weather Extractor - Options Page Script

class WeatherExtractorOptions {
    constructor() {
        this.settings = {};
        this.defaultSettings = {
            defaultLat: 30.1833,
            defaultLon: 120.2,
            showNotifications: true,
            autoSave: true,
            theme: 'auto',
            extractionTimeout: 300,
            delayBetweenRequests: 2000,
            retryAttempts: 3,
            maxDays: 14,
            defaultHours: [2, 5, 8, 11, 17, 23],
            defaultDataTypes: ['temperature', 'precipitation', 'cloudcover', 'windspeed'],
            defaultExportFormat: 'json',
            filenameTemplate: 'ventusky_weather_{date}',
            includeMetadata: true,
            compressFiles: false,
            maxHistoryItems: 50,
            historyRetentionDays: 30,
            enableDebugMode: false,
            enablePerformanceMonitoring: false
        };
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadSettings();
        this.updateVersion();
        this.loadStatistics();
    }

    initializeElements() {
        // 标签按钮
        this.tabButtons = document.querySelectorAll('.tab-btn');
        this.tabContents = document.querySelectorAll('.tab-content');
        
        // 表单元素
        this.defaultLatInput = document.getElementById('defaultLat');
        this.defaultLonInput = document.getElementById('defaultLon');
        this.showNotificationsInput = document.getElementById('showNotifications');
        this.autoSaveInput = document.getElementById('autoSave');
        this.themeSelect = document.getElementById('theme');
        
        this.extractionTimeoutInput = document.getElementById('extractionTimeout');
        this.delayBetweenRequestsInput = document.getElementById('delayBetweenRequests');
        this.retryAttemptsInput = document.getElementById('retryAttempts');
        this.maxDaysInput = document.getElementById('maxDays');
        
        this.defaultExportFormatSelect = document.getElementById('defaultExportFormat');
        this.filenameTemplateInput = document.getElementById('filenameTemplate');
        this.includeMetadataInput = document.getElementById('includeMetadata');
        this.compressFilesInput = document.getElementById('compressFiles');
        
        this.maxHistoryItemsInput = document.getElementById('maxHistoryItems');
        this.historyRetentionDaysInput = document.getElementById('historyRetentionDays');
        
        this.enableDebugModeInput = document.getElementById('enableDebugMode');
        this.enablePerformanceMonitoringInput = document.getElementById('enablePerformanceMonitoring');
        
        // 按钮
        this.saveBtn = document.getElementById('saveBtn');
        this.resetBtn = document.getElementById('resetBtn');
        this.clearHistoryBtn = document.getElementById('clearHistoryBtn');
        this.exportHistoryBtn = document.getElementById('exportHistoryBtn');
        this.resetSettingsBtn = document.getElementById('resetSettingsBtn');
        this.exportSettingsBtn = document.getElementById('exportSettingsBtn');
        this.importSettingsBtn = document.getElementById('importSettingsBtn');
        this.importSettingsFile = document.getElementById('importSettingsFile');
        this.clearAllDataBtn = document.getElementById('clearAllDataBtn');
        
        // 状态显示
        this.saveStatus = document.getElementById('saveStatus');
        this.versionElement = document.getElementById('version');
        this.aboutVersionElement = document.getElementById('aboutVersion');
        
        // 统计元素
        this.totalExtractionsElement = document.getElementById('totalExtractions');
        this.totalDaysElement = document.getElementById('totalDays');
        this.successRateElement = document.getElementById('successRate');
        this.lastUsedElement = document.getElementById('lastUsed');
    }

    setupEventListeners() {
        // 标签切换
        this.tabButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });
        
        // 预设位置按钮
        document.querySelectorAll('.location-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.defaultLatInput.value = e.target.dataset.lat;
                this.defaultLonInput.value = e.target.dataset.lon;
            });
        });
        
        // 保存和重置按钮
        this.saveBtn.addEventListener('click', () => this.saveSettings());
        this.resetBtn.addEventListener('click', () => this.resetCurrentTab());
        
        // 历史记录管理
        this.clearHistoryBtn.addEventListener('click', () => this.clearHistory());
        this.exportHistoryBtn.addEventListener('click', () => this.exportHistory());
        
        // 高级设置
        this.resetSettingsBtn.addEventListener('click', () => this.resetAllSettings());
        this.exportSettingsBtn.addEventListener('click', () => this.exportSettings());
        this.importSettingsBtn.addEventListener('click', () => this.importSettingsFile.click());
        this.importSettingsFile.addEventListener('change', (e) => this.importSettings(e));
        this.clearAllDataBtn.addEventListener('click', () => this.clearAllData());
        
        // 链接处理
        document.getElementById('helpLink').addEventListener('click', () => this.openHelp());
        document.getElementById('feedbackLink').addEventListener('click', () => this.openFeedback());
        document.getElementById('updateLogLink').addEventListener('click', () => this.openUpdateLog());
        
        // 实时保存（可选）
        document.querySelectorAll('input, select').forEach(element => {
            element.addEventListener('change', () => {
                if (this.autoSaveInput && this.autoSaveInput.checked) {
                    this.saveSettings(false); // 静默保存
                }
            });
        });
    }

    switchTab(tabName) {
        // 更新按钮状态
        this.tabButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });
        
        // 更新内容显示
        this.tabContents.forEach(content => {
            content.classList.toggle('active', content.id === tabName);
        });
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(this.defaultSettings);
            this.settings = { ...this.defaultSettings, ...result };
            this.updateUI();
        } catch (error) {
            console.error('加载设置失败:', error);
            this.showStatus('加载设置失败', 'error');
        }
    }

    updateUI() {
        // 基本设置
        if (this.defaultLatInput) this.defaultLatInput.value = this.settings.defaultLat;
        if (this.defaultLonInput) this.defaultLonInput.value = this.settings.defaultLon;
        if (this.showNotificationsInput) this.showNotificationsInput.checked = this.settings.showNotifications;
        if (this.autoSaveInput) this.autoSaveInput.checked = this.settings.autoSave;
        if (this.themeSelect) this.themeSelect.value = this.settings.theme;
        
        // 提取设置
        if (this.extractionTimeoutInput) this.extractionTimeoutInput.value = this.settings.extractionTimeout;
        if (this.delayBetweenRequestsInput) this.delayBetweenRequestsInput.value = this.settings.delayBetweenRequests;
        if (this.retryAttemptsInput) this.retryAttemptsInput.value = this.settings.retryAttempts;
        if (this.maxDaysInput) this.maxDaysInput.value = this.settings.maxDays;
        
        // 默认时间点
        document.querySelectorAll('.default-hour').forEach(checkbox => {
            checkbox.checked = this.settings.defaultHours.includes(parseInt(checkbox.value));
        });
        
        // 默认数据类型
        document.querySelectorAll('.default-data').forEach(checkbox => {
            checkbox.checked = this.settings.defaultDataTypes.includes(checkbox.value);
        });
        
        // 导出设置
        if (this.defaultExportFormatSelect) this.defaultExportFormatSelect.value = this.settings.defaultExportFormat;
        if (this.filenameTemplateInput) this.filenameTemplateInput.value = this.settings.filenameTemplate;
        if (this.includeMetadataInput) this.includeMetadataInput.checked = this.settings.includeMetadata;
        if (this.compressFilesInput) this.compressFilesInput.checked = this.settings.compressFiles;
        
        // 历史记录设置
        if (this.maxHistoryItemsInput) this.maxHistoryItemsInput.value = this.settings.maxHistoryItems;
        if (this.historyRetentionDaysInput) this.historyRetentionDaysInput.value = this.settings.historyRetentionDays;
        
        // 高级设置
        if (this.enableDebugModeInput) this.enableDebugModeInput.checked = this.settings.enableDebugMode;
        if (this.enablePerformanceMonitoringInput) this.enablePerformanceMonitoringInput.checked = this.settings.enablePerformanceMonitoring;
    }

    async saveSettings(showMessage = true) {
        try {
            // 收集当前设置
            const newSettings = {
                defaultLat: parseFloat(this.defaultLatInput?.value || this.settings.defaultLat),
                defaultLon: parseFloat(this.defaultLonInput?.value || this.settings.defaultLon),
                showNotifications: this.showNotificationsInput?.checked ?? this.settings.showNotifications,
                autoSave: this.autoSaveInput?.checked ?? this.settings.autoSave,
                theme: this.themeSelect?.value || this.settings.theme,
                
                extractionTimeout: parseInt(this.extractionTimeoutInput?.value || this.settings.extractionTimeout),
                delayBetweenRequests: parseInt(this.delayBetweenRequestsInput?.value || this.settings.delayBetweenRequests),
                retryAttempts: parseInt(this.retryAttemptsInput?.value || this.settings.retryAttempts),
                maxDays: parseInt(this.maxDaysInput?.value || this.settings.maxDays),
                
                defaultHours: Array.from(document.querySelectorAll('.default-hour:checked')).map(cb => parseInt(cb.value)),
                defaultDataTypes: Array.from(document.querySelectorAll('.default-data:checked')).map(cb => cb.value),
                
                defaultExportFormat: this.defaultExportFormatSelect?.value || this.settings.defaultExportFormat,
                filenameTemplate: this.filenameTemplateInput?.value || this.settings.filenameTemplate,
                includeMetadata: this.includeMetadataInput?.checked ?? this.settings.includeMetadata,
                compressFiles: this.compressFilesInput?.checked ?? this.settings.compressFiles,
                
                maxHistoryItems: parseInt(this.maxHistoryItemsInput?.value || this.settings.maxHistoryItems),
                historyRetentionDays: parseInt(this.historyRetentionDaysInput?.value || this.settings.historyRetentionDays),
                
                enableDebugMode: this.enableDebugModeInput?.checked ?? this.settings.enableDebugMode,
                enablePerformanceMonitoring: this.enablePerformanceMonitoringInput?.checked ?? this.settings.enablePerformanceMonitoring
            };
            
            // 保存到Chrome存储
            await chrome.storage.sync.set(newSettings);
            this.settings = newSettings;
            
            if (showMessage) {
                this.showStatus('设置保存成功', 'success');
            }
            
        } catch (error) {
            console.error('保存设置失败:', error);
            this.showStatus('保存设置失败', 'error');
        }
    }

    resetCurrentTab() {
        const activeTab = document.querySelector('.tab-content.active');
        if (!activeTab) return;
        
        const tabId = activeTab.id;
        
        // 根据当前标签重置相应设置
        switch (tabId) {
            case 'general':
                this.defaultLatInput.value = this.defaultSettings.defaultLat;
                this.defaultLonInput.value = this.defaultSettings.defaultLon;
                this.showNotificationsInput.checked = this.defaultSettings.showNotifications;
                this.autoSaveInput.checked = this.defaultSettings.autoSave;
                this.themeSelect.value = this.defaultSettings.theme;
                break;
                
            case 'extraction':
                this.extractionTimeoutInput.value = this.defaultSettings.extractionTimeout;
                this.delayBetweenRequestsInput.value = this.defaultSettings.delayBetweenRequests;
                this.retryAttemptsInput.value = this.defaultSettings.retryAttempts;
                this.maxDaysInput.value = this.defaultSettings.maxDays;
                
                document.querySelectorAll('.default-hour').forEach(checkbox => {
                    checkbox.checked = this.defaultSettings.defaultHours.includes(parseInt(checkbox.value));
                });
                
                document.querySelectorAll('.default-data').forEach(checkbox => {
                    checkbox.checked = this.defaultSettings.defaultDataTypes.includes(checkbox.value);
                });
                break;
                
            case 'export':
                this.defaultExportFormatSelect.value = this.defaultSettings.defaultExportFormat;
                this.filenameTemplateInput.value = this.defaultSettings.filenameTemplate;
                this.includeMetadataInput.checked = this.defaultSettings.includeMetadata;
                this.compressFilesInput.checked = this.defaultSettings.compressFiles;
                this.maxHistoryItemsInput.value = this.defaultSettings.maxHistoryItems;
                this.historyRetentionDaysInput.value = this.defaultSettings.historyRetentionDays;
                break;
                
            case 'advanced':
                this.enableDebugModeInput.checked = this.defaultSettings.enableDebugMode;
                this.enablePerformanceMonitoringInput.checked = this.defaultSettings.enablePerformanceMonitoring;
                break;
        }
        
        this.showStatus('当前标签已重置', 'success');
    }

    async clearHistory() {
        if (!confirm('确定要清空所有历史记录吗？此操作不可撤销。')) {
            return;
        }
        
        try {
            await chrome.storage.local.remove(['extractionHistory']);
            this.showStatus('历史记录已清空', 'success');
            this.loadStatistics(); // 更新统计信息
        } catch (error) {
            console.error('清空历史记录失败:', error);
            this.showStatus('清空历史记录失败', 'error');
        }
    }

    async exportHistory() {
        try {
            const result = await chrome.storage.local.get(['extractionHistory']);
            const history = result.extractionHistory || [];
            
            if (history.length === 0) {
                this.showStatus('没有历史记录可导出', 'error');
                return;
            }
            
            const blob = new Blob([JSON.stringify(history, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ventusky_history_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            this.showStatus('历史记录导出成功', 'success');
        } catch (error) {
            console.error('导出历史记录失败:', error);
            this.showStatus('导出历史记录失败', 'error');
        }
    }

    async resetAllSettings() {
        if (!confirm('确定要重置所有设置吗？此操作不可撤销。')) {
            return;
        }
        
        try {
            await chrome.storage.sync.clear();
            this.settings = { ...this.defaultSettings };
            this.updateUI();
            this.showStatus('所有设置已重置', 'success');
        } catch (error) {
            console.error('重置设置失败:', error);
            this.showStatus('重置设置失败', 'error');
        }
    }

    async exportSettings() {
        try {
            const blob = new Blob([JSON.stringify(this.settings, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ventusky_settings_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            this.showStatus('设置导出成功', 'success');
        } catch (error) {
            console.error('导出设置失败:', error);
            this.showStatus('导出设置失败', 'error');
        }
    }

    async importSettings(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        try {
            const text = await file.text();
            const importedSettings = JSON.parse(text);
            
            // 验证设置格式
            const validSettings = {};
            for (const [key, defaultValue] of Object.entries(this.defaultSettings)) {
                if (importedSettings.hasOwnProperty(key)) {
                    validSettings[key] = importedSettings[key];
                } else {
                    validSettings[key] = defaultValue;
                }
            }
            
            await chrome.storage.sync.set(validSettings);
            this.settings = validSettings;
            this.updateUI();
            
            this.showStatus('设置导入成功', 'success');
        } catch (error) {
            console.error('导入设置失败:', error);
            this.showStatus('导入设置失败，请检查文件格式', 'error');
        }
        
        // 清空文件输入
        event.target.value = '';
    }

    async clearAllData() {
        if (!confirm('确定要清空所有数据吗？这将删除所有设置和历史记录，此操作不可撤销。')) {
            return;
        }
        
        try {
            await chrome.storage.sync.clear();
            await chrome.storage.local.clear();
            
            this.settings = { ...this.defaultSettings };
            this.updateUI();
            this.loadStatistics();
            
            this.showStatus('所有数据已清空', 'success');
        } catch (error) {
            console.error('清空数据失败:', error);
            this.showStatus('清空数据失败', 'error');
        }
    }

    async loadStatistics() {
        try {
            const result = await chrome.storage.local.get(['extractionHistory']);
            const history = result.extractionHistory || [];
            
            const totalExtractions = history.length;
            const totalDays = history.reduce((sum, record) => sum + (record.totalDays || 0), 0);
            const successfulDays = history.reduce((sum, record) => sum + (record.successfulDays || 0), 0);
            const successRate = totalDays > 0 ? Math.round((successfulDays / totalDays) * 100) : 0;
            const lastUsed = history.length > 0 ? new Date(history[0].timestamp).toLocaleDateString() : '从未';
            
            if (this.totalExtractionsElement) this.totalExtractionsElement.textContent = totalExtractions;
            if (this.totalDaysElement) this.totalDaysElement.textContent = totalDays;
            if (this.successRateElement) this.successRateElement.textContent = `${successRate}%`;
            if (this.lastUsedElement) this.lastUsedElement.textContent = lastUsed;
            
        } catch (error) {
            console.error('加载统计信息失败:', error);
        }
    }

    updateVersion() {
        const version = chrome.runtime.getManifest().version;
        if (this.versionElement) this.versionElement.textContent = `v${version}`;
        if (this.aboutVersionElement) this.aboutVersionElement.textContent = version;
    }

    showStatus(message, type = 'success') {
        if (!this.saveStatus) return;
        
        this.saveStatus.textContent = message;
        this.saveStatus.className = `status ${type}`;
        
        // 3秒后清除状态
        setTimeout(() => {
            this.saveStatus.textContent = '';
            this.saveStatus.className = 'status';
        }, 3000);
    }

    openHelp() {
        chrome.tabs.create({ url: 'https://www.ventusky.com' });
    }

    openFeedback() {
        // TODO: 实现反馈页面
        alert('反馈功能即将推出！');
    }

    openUpdateLog() {
        // TODO: 实现更新日志
        alert('更新日志功能即将推出！');
    }
}

// 初始化选项页面
document.addEventListener('DOMContentLoaded', () => {
    new WeatherExtractorOptions();
});
