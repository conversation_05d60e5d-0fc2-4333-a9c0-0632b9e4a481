// Ventusky Weather Extractor - Popup Script

class WeatherExtractorPopup {
    constructor() {
        this.isExtracting = false;
        this.currentProgress = 0;
        this.totalDays = 0;
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadSettings();
        this.setDefaultDates();
        this.updateStatus('ready', '准备就绪');
    }

    initializeElements() {
        // 状态元素
        this.statusDot = document.getElementById('statusDot');
        this.statusText = document.getElementById('statusText');
        
        // 输入元素
        this.latitudeInput = document.getElementById('latitude');
        this.longitudeInput = document.getElementById('longitude');
        this.startDateInput = document.getElementById('startDate');
        this.endDateInput = document.getElementById('endDate');
        
        // 按钮元素
        this.startBtn = document.getElementById('startExtraction');
        this.stopBtn = document.getElementById('stopExtraction');
        this.getCurrentLocationBtn = document.getElementById('getCurrentLocation');
        
        // 进度元素
        this.progressSection = document.getElementById('progressSection');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');
        
        // 结果元素
        this.resultsSection = document.getElementById('resultsSection');
        this.resultsInfo = document.getElementById('resultsInfo');
        this.downloadButtons = document.getElementById('downloadButtons');
    }

    setupEventListeners() {
        // 开始提取按钮
        this.startBtn.addEventListener('click', () => this.startExtraction());
        
        // 停止提取按钮
        this.stopBtn.addEventListener('click', () => this.stopExtraction());
        
        // 获取当前位置按钮
        this.getCurrentLocationBtn.addEventListener('click', () => this.getCurrentLocation());
        
        // 快速日期按钮
        document.querySelectorAll('.btn-quick').forEach(btn => {
            btn.addEventListener('click', (e) => this.setQuickDate(parseInt(e.target.dataset.days)));
        });
        
        // 设置链接
        document.getElementById('openOptions').addEventListener('click', () => {
            chrome.runtime.openOptionsPage();
        });
        
        // 历史记录链接
        document.getElementById('viewHistory').addEventListener('click', () => {
            this.showHistory();
        });
        
        // 帮助链接
        document.getElementById('helpLink').addEventListener('click', () => {
            this.showHelp();
        });

        // 监听来自content script的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
        });
    }

    setDefaultDates() {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        this.startDateInput.value = today.toISOString().split('T')[0];
        this.endDateInput.value = tomorrow.toISOString().split('T')[0];
    }

    setQuickDate(days) {
        const today = new Date();
        const endDate = new Date(today);
        endDate.setDate(endDate.getDate() + days - 1);
        
        this.startDateInput.value = today.toISOString().split('T')[0];
        this.endDateInput.value = endDate.toISOString().split('T')[0];
        
        // 更新按钮状态
        document.querySelectorAll('.btn-quick').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
    }

    async getCurrentLocation() {
        try {
            this.updateStatus('working', '获取当前页面坐标...');
            
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab.url.includes('ventusky.com')) {
                this.updateStatus('error', '请在Ventusky页面使用此功能');
                return;
            }
            
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'getCurrentCoordinates'
            });
            
            if (response && response.success) {
                this.latitudeInput.value = response.lat.toFixed(4);
                this.longitudeInput.value = response.lon.toFixed(4);
                this.updateStatus('success', '坐标获取成功');
            } else {
                this.updateStatus('error', '无法获取坐标');
            }
        } catch (error) {
            console.error('获取坐标失败:', error);
            this.updateStatus('error', '获取坐标失败');
        }
    }

    async startExtraction() {
        if (this.isExtracting) return;
        
        // 验证输入
        const config = this.getExtractionConfig();
        if (!this.validateConfig(config)) {
            return;
        }
        
        this.isExtracting = true;
        this.updateUI(true);
        this.updateStatus('working', '开始提取天气数据...');
        
        try {
            // 检查是否在Ventusky页面
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab.url.includes('ventusky.com')) {
                throw new Error('请在Ventusky页面使用此插件');
            }
            
            // 发送提取请求到content script
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'startExtraction',
                config: config
            });
            
            if (!response || !response.success) {
                throw new Error(response?.error || '启动提取失败');
            }
            
            this.totalDays = config.dateList.length;
            this.showProgress();
            
        } catch (error) {
            console.error('提取失败:', error);
            this.updateStatus('error', error.message);
            this.stopExtraction();
        }
    }

    stopExtraction() {
        this.isExtracting = false;
        this.updateUI(false);
        this.updateStatus('ready', '提取已停止');
        
        // 发送停止消息到content script
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, { action: 'stopExtraction' });
            }
        });
    }

    getExtractionConfig() {
        // 获取选中的时间点
        const selectedHours = Array.from(document.querySelectorAll('.time-selection input:checked'))
            .map(input => parseInt(input.value));
        
        // 获取选中的数据类型
        const selectedDataTypes = Array.from(document.querySelectorAll('.data-types input:checked'))
            .map(input => input.value);
        
        // 生成日期列表
        const startDate = new Date(this.startDateInput.value);
        const endDate = new Date(this.endDateInput.value);
        const dateList = [];
        
        for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
            dateList.push(d.toISOString().split('T')[0]);
        }
        
        return {
            lat: parseFloat(this.latitudeInput.value),
            lon: parseFloat(this.longitudeInput.value),
            startDate: this.startDateInput.value,
            endDate: this.endDateInput.value,
            dateList: dateList,
            selectedHours: selectedHours,
            selectedDataTypes: selectedDataTypes
        };
    }

    validateConfig(config) {
        if (isNaN(config.lat) || config.lat < -90 || config.lat > 90) {
            this.updateStatus('error', '纬度必须在-90到90之间');
            return false;
        }
        
        if (isNaN(config.lon) || config.lon < -180 || config.lon > 180) {
            this.updateStatus('error', '经度必须在-180到180之间');
            return false;
        }
        
        if (config.dateList.length === 0) {
            this.updateStatus('error', '请选择有效的日期范围');
            return false;
        }
        
        if (config.dateList.length > 14) {
            this.updateStatus('error', '日期范围不能超过14天');
            return false;
        }
        
        if (config.selectedHours.length === 0) {
            this.updateStatus('error', '请至少选择一个时间点');
            return false;
        }
        
        if (config.selectedDataTypes.length === 0) {
            this.updateStatus('error', '请至少选择一种数据类型');
            return false;
        }
        
        return true;
    }

    updateUI(isExtracting) {
        this.startBtn.style.display = isExtracting ? 'none' : 'flex';
        this.stopBtn.style.display = isExtracting ? 'flex' : 'none';
        
        // 禁用/启用输入控件
        const inputs = document.querySelectorAll('input, button:not(#stopExtraction)');
        inputs.forEach(input => {
            input.disabled = isExtracting;
        });
    }

    showProgress() {
        this.progressSection.style.display = 'block';
        this.updateProgress(0);
    }

    updateProgress(completed) {
        this.currentProgress = completed;
        const percentage = (completed / this.totalDays) * 100;
        
        this.progressFill.style.width = `${percentage}%`;
        this.progressText.textContent = `${completed}/${this.totalDays} 天完成`;
        
        if (completed === this.totalDays) {
            this.updateStatus('success', '数据提取完成！');
        }
    }

    updateStatus(type, message) {
        this.statusDot.className = `status-dot ${type}`;
        this.statusText.textContent = message;
    }

    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'updateProgress':
                this.updateProgress(message.completed);
                this.updateStatus('working', `正在提取第${message.completed + 1}天数据...`);
                break;
                
            case 'extractionComplete':
                this.showResults(message.results);
                this.stopExtraction();
                break;
                
            case 'extractionError':
                this.updateStatus('error', message.error);
                this.stopExtraction();
                break;
        }
    }

    showResults(results) {
        this.resultsSection.style.display = 'block';
        
        const successDays = Object.keys(results.weatherData).length;
        const totalDays = results.totalDays;
        
        this.resultsInfo.innerHTML = `
            <strong>提取完成！</strong><br>
            成功获取 ${successDays}/${totalDays} 天的天气数据<br>
            位置: ${results.location}<br>
            时间: ${new Date().toLocaleString()}
        `;
        
        // 创建下载按钮
        this.downloadButtons.innerHTML = `
            <button onclick="downloadJSON()">📄 JSON</button>
            <button onclick="downloadCSV()">📊 CSV</button>
            <button onclick="downloadExcel()">📈 Excel</button>
        `;
        
        // 保存结果到storage
        chrome.storage.local.set({
            lastResults: results,
            lastExtraction: Date.now()
        });
    }

    loadSettings() {
        chrome.storage.sync.get(['defaultLat', 'defaultLon'], (result) => {
            if (result.defaultLat) this.latitudeInput.value = result.defaultLat;
            if (result.defaultLon) this.longitudeInput.value = result.defaultLon;
        });
    }

    showHistory() {
        // TODO: 实现历史记录显示
        alert('历史记录功能即将推出！');
    }

    showHelp() {
        // TODO: 实现帮助页面
        alert('帮助文档即将推出！');
    }
}

// 全局下载函数
window.downloadJSON = function() {
    chrome.storage.local.get(['lastResults'], (result) => {
        if (result.lastResults) {
            const blob = new Blob([JSON.stringify(result.lastResults, null, 2)], 
                { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ventusky_weather_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
    });
};

window.downloadCSV = function() {
    // TODO: 实现CSV下载
    alert('CSV下载功能开发中...');
};

window.downloadExcel = function() {
    // TODO: 实现Excel下载
    alert('Excel下载功能开发中...');
};

// 初始化popup
document.addEventListener('DOMContentLoaded', () => {
    new WeatherExtractorPopup();
});
