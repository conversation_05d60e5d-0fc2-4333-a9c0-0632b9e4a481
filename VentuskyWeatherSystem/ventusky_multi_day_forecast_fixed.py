#!/usr/bin/env python3
"""
修正版多日天气预报获取系统：7月25号到8月7号（14天）
修正：每次切换日期后重新设置图层顺序
"""

import json
import time
import re
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

# Excel相关导入
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

class VentuskyMultiDayForecastFixed:
    def __init__(self, lat=30.1833, lon=120.2, start_date="2025-07-25", end_date="2025-08-07"):
        self.driver = None
        self.target_lat = lat
        self.target_lon = lon
        self.start_date = start_date
        self.end_date = end_date
        self.target_hours = [2, 5, 8, 11, 17, 23]
        self.date_list = self.generate_date_list()
        self.all_weather_data = {}
        self.current_date_index = 0  # 跟踪当前日期索引
        self.setup_driver()
        
    def generate_date_list(self):
        """生成日期列表"""
        start = datetime.strptime(self.start_date, "%Y-%m-%d")
        end = datetime.strptime(self.end_date, "%Y-%m-%d")
        
        date_list = []
        current = start
        while current <= end:
            date_list.append(current.strftime("%Y-%m-%d"))
            current += timedelta(days=1)
        
        return date_list
        
    def setup_driver(self):
        """设置Chrome驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            print("✓ Chrome驱动设置成功")
        except Exception as e:
            print(f"❌ Chrome驱动设置失败: {e}")
            raise

    def setup_page(self):
        """设置页面"""
        print(f"🎯 导航到位置: {self.format_coordinates()}")
        
        target_url = f"https://www.ventusky.com/?p={self.target_lat};{self.target_lon};11&l=temperature-2m"
        self.driver.get(target_url)
        
        WebDriverWait(self.driver, 20).until(
            EC.presence_of_element_located((By.TAG_NAME, "canvas"))
        )
        time.sleep(10)
        print("✓ 页面加载完成")

    def format_coordinates(self):
        """格式化坐标显示"""
        lat_deg = int(self.target_lat)
        lat_min = int((self.target_lat - lat_deg) * 60)
        lon_deg = int(self.target_lon)
        lon_min = int((self.target_lon - lon_deg) * 60)
        return f"{lat_deg}°{lat_min}' {lon_deg}°{lon_min}'"

    def click_date_selector_to_change_date(self, target_date):
        """点击日期选择器来更改日期（改进版）"""
        from datetime import datetime

        print(f"📅 点击日期选择器更改到: {target_date}")

        try:
            target_dt = datetime.strptime(target_date, "%Y-%m-%d")
            target_day = target_dt.day
            target_month = target_dt.month

            print(f"目标日期: {target_month}月{target_day}号")

            # 第一步：先点击打开日期选择器
            print("🔍 第一步：查找并点击日期选择器...")
            date_opener_found = False

            # 查找可能的日期选择器开启元素
            opener_selectors = [
                "//span[contains(text(), '今天')]",
                "//span[contains(text(), '2025')]",
                "//*[contains(@class, 'date') and contains(@class, 'picker')]",
                "//div[contains(@class, 'date-selector')]",
                "//button[contains(@class, 'date')]"
            ]

            for selector in opener_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            print(f"找到日期选择器开启元素: '{element.text}' - 标签: {element.tag_name}")

                            # 点击打开日期选择器
                            actions = ActionChains(self.driver)
                            actions.move_to_element(element).click().perform()
                            time.sleep(2)

                            print("✅ 成功点击打开日期选择器")
                            date_opener_found = True
                            break

                    if date_opener_found:
                        break
                except:
                    continue

            if not date_opener_found:
                print("⚠️ 未找到日期选择器开启元素，尝试直接查找日期")

            # 第二步：等待日期选择器展开，然后查找目标日期
            print(f"🔍 第二步：查找目标日期 {target_day}...")
            time.sleep(3)  # 等待日期选择器完全展开

            # 先列出所有可能的日期元素进行调试
            print("🔍 调试：列出所有可见的日期相关元素...")
            all_elements = self.driver.find_elements(By.XPATH, "//*[string-length(text()) <= 2 and text() != '']")
            for i, el in enumerate(all_elements[:20]):  # 只显示前20个
                try:
                    if el.is_displayed():
                        print(f"  元素{i}: 文本='{el.text}', 标签={el.tag_name}, 类名={el.get_attribute('class')}")
                except:
                    continue

            # 更精确的日期查找策略
            day_selectors = [
                # 最精确的匹配：完全匹配数字且可点击
                f"//span[text()='{target_day}' and not(contains(@class, 'disabled')) and not(contains(@class, 'other-month'))]",
                f"//div[text()='{target_day}' and not(contains(@class, 'disabled')) and not(contains(@class, 'other-month'))]",
                f"//button[text()='{target_day}' and not(contains(@class, 'disabled'))]",
                f"//td[text()='{target_day}' and not(contains(@class, 'disabled'))]",
                # 包含day类的元素
                f"//*[text()='{target_day}' and contains(@class, 'day') and not(contains(@class, 'disabled'))]",
                # 通用匹配
                f"//*[text()='{target_day}' and not(contains(@class, 'disabled'))]"
            ]

            target_clicked = False

            for selector_index, selector in enumerate(day_selectors):
                try:
                    day_elements = self.driver.find_elements(By.XPATH, selector)
                    print(f"🔍 选择器 {selector_index+1}: '{selector}' 找到 {len(day_elements)} 个元素")

                    for element_index, element in enumerate(day_elements):
                        try:
                            if element.is_displayed() and element.is_enabled():
                                element_text = element.text.strip()
                                element_class = element.get_attribute('class') or ''
                                element_tag = element.tag_name

                                print(f"  元素{element_index}: 文本='{element_text}', 标签={element_tag}, 类名='{element_class}'")

                                # 严格验证：必须是目标日期数字，且不能是禁用或其他月份
                                if (element_text == str(target_day) and
                                    'disabled' not in element_class.lower() and
                                    'other-month' not in element_class.lower()):

                                    print(f"✅ 验证通过，准备点击目标日期元素: {target_day}")

                                    # 高亮显示要点击的元素（调试用）
                                    self.driver.execute_script("arguments[0].style.border='3px solid red';", element)
                                    time.sleep(1)

                                    # 滚动到元素可见
                                    self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                    time.sleep(1)

                                    # 尝试多种点击方式
                                    click_success = False

                                    # 方式1：ActionChains点击
                                    try:
                                        actions = ActionChains(self.driver)
                                        actions.move_to_element(element).click().perform()
                                        time.sleep(2)
                                        click_success = True
                                        print("✅ ActionChains点击成功")
                                    except Exception as e:
                                        print(f"ActionChains点击失败: {e}")

                                    # 方式2：直接点击
                                    if not click_success:
                                        try:
                                            element.click()
                                            time.sleep(2)
                                            click_success = True
                                            print("✅ 直接点击成功")
                                        except Exception as e:
                                            print(f"直接点击失败: {e}")

                                    # 方式3：JavaScript点击
                                    if not click_success:
                                        try:
                                            self.driver.execute_script("arguments[0].click();", element)
                                            time.sleep(2)
                                            click_success = True
                                            print("✅ JavaScript点击成功")
                                        except Exception as e:
                                            print(f"JavaScript点击失败: {e}")

                                    if click_success:
                                        # 验证日期是否真的改变了
                                        time.sleep(3)
                                        current_date_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '今天') or contains(text(), '2025')]")
                                        for date_el in current_date_elements:
                                            if date_el.is_displayed():
                                                print(f"🔍 当前页面日期显示: '{date_el.text}'")

                                        print(f"✅ 成功点击日期 {target_day}")
                                        target_clicked = True
                                        break
                                    else:
                                        print(f"❌ 所有点击方式都失败")

                        except Exception as e:
                            print(f"处理元素失败: {e}")
                            continue

                    if target_clicked:
                        break

                except Exception as e:
                    print(f"选择器 '{selector}' 执行失败: {e}")
                    continue

            if not target_clicked:
                print(f"❌ 未能点击到目标日期 {target_day}")

                # 最后尝试：使用JavaScript直接点击
                print("🔄 最后尝试：使用JavaScript查找并点击...")
                try:
                    js_script = f"""
                    var elements = document.querySelectorAll('*');
                    for (var i = 0; i < elements.length; i++) {{
                        var el = elements[i];
                        if (el.textContent.trim() === '{target_day}' &&
                            el.offsetParent !== null &&
                            !el.classList.contains('disabled')) {{
                            el.click();
                            return true;
                        }}
                    }}
                    return false;
                    """

                    result = self.driver.execute_script(js_script)
                    if result:
                        print(f"✅ JavaScript成功点击日期 {target_day}")
                        time.sleep(3)
                        return True
                    else:
                        print(f"❌ JavaScript也未能找到日期 {target_day}")
                        return False

                except Exception as e:
                    print(f"❌ JavaScript点击失败: {e}")
                    return False

            return target_clicked

        except Exception as e:
            print(f"❌ 点击日期选择器失败: {e}")
            return False

    def navigate_to_next_date(self):
        """导航到下一个日期（每次只前进一天）"""
        print(f"📅 前进到下一天（第{self.current_date_index + 1}天）...")

        try:
            from selenium.webdriver.common.keys import Keys
            self.driver.find_element(By.TAG_NAME, "body").click()
            time.sleep(2)

            actions = ActionChains(self.driver)
            actions.send_keys(Keys.ARROW_RIGHT).perform()
            time.sleep(5)  # 增加等待时间确保页面更新

            print(f"✅ 已前进到第{self.current_date_index + 1}天")
            return True

        except Exception as e:
            print(f"❌ 导航失败: {e}")
            return False

    def reset_to_temperature_layer(self):
        """重置到温度图层（确保每次都从温度开始）"""
        print("🔄 重置到温度图层...")
        
        try:
            # 查找温度选项
            temp_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '温度')]")
            
            for element in temp_elements:
                try:
                    if element.is_displayed() and element.is_enabled():
                        element.click()
                        time.sleep(3)
                        print("✅ 已重置到温度图层")
                        return True
                except:
                    continue
            
            # 如果没找到中文的，尝试英文
            temp_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Temperature') or contains(text(), 'temperature')]")
            
            for element in temp_elements:
                try:
                    if element.is_displayed() and element.is_enabled():
                        element.click()
                        time.sleep(3)
                        print("✅ 已重置到温度图层")
                        return True
                except:
                    continue
            
            print("⚠️ 未找到温度选项，假设已在温度图层")
            return True
            
        except Exception as e:
            print(f"❌ 重置到温度图层失败: {e}")
            return False

    def find_timeline_elements(self):
        """找到时间轴元素"""
        timeline_elements = []
        all_links = self.driver.find_elements(By.TAG_NAME, "a")
        
        for link in all_links:
            try:
                if link.is_displayed():
                    location = link.location
                    size = link.size
                    
                    if (location['y'] > 800 and  
                        size['width'] > 30 and size['width'] < 80 and  
                        size['height'] > 20 and size['height'] < 50):
                        
                        timeline_elements.append({
                            'element': link,
                            'x_position': location['x']
                        })
            except:
                continue
        
        timeline_elements.sort(key=lambda x: x['x_position'])
        return timeline_elements

    def get_data_for_all_hours(self, data_type):
        """获取所有时间点的数据"""
        timeline_elements = self.find_timeline_elements()
        if not timeline_elements:
            return {}
        
        total_elements = len(timeline_elements)
        data_results = {}
        
        for target_hour in self.target_hours:
            try:
                # 点击时间轴
                hours_per_element = 24 / total_elements
                element_index = int(target_hour / hours_per_element)
                element_index = max(0, min(element_index, total_elements - 1))
                
                element = timeline_elements[element_index]['element']
                actions = ActionChains(self.driver)
                actions.move_to_element(element).click().perform()
                time.sleep(2)
                
                # 悬停获取数据
                canvas = self.driver.find_element(By.TAG_NAME, "canvas")
                canvas_size = canvas.size
                center_x = canvas_size['width'] // 2
                center_y = canvas_size['height'] // 2
                
                actions = ActionChains(self.driver)
                actions.move_to_element_with_offset(canvas, center_x, center_y).perform()
                time.sleep(2)
                
                # 提取数据
                if data_type == "温度":
                    hour_data = self.extract_temperature_data()
                elif data_type == "降水量":
                    hour_data = self.extract_precipitation_data()
                elif data_type == "云量":
                    hour_data = self.extract_cloudcover_data()
                elif data_type == "风速":
                    hour_data = self.extract_windspeed_data()
                
                if hour_data:
                    data_results[f"{target_hour:02d}:00"] = hour_data
                
            except Exception as e:
                continue
        
        return data_results

    def extract_temperature_data(self):
        """提取温度数据"""
        try:
            return self.driver.execute_script("""
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    if (element.offsetParent !== null) {
                        var text = element.textContent || element.innerText || '';
                        var tempMatch = text.match(/(-?\\d+)\\s*°C?/);
                        if (tempMatch) {
                            var tempValue = parseInt(tempMatch[1]);
                            if (tempValue >= -50 && tempValue <= 60) {
                                return {temperature: tempValue + '°C'};
                            }
                        }
                    }
                }
                return null;
            """)
        except:
            return None

    def extract_precipitation_data(self):
        """提取降水量数据（支持中文句号）"""
        try:
            return self.driver.execute_script("""
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    if (element.offsetParent !== null) {
                        var text = element.textContent || element.innerText || '';
                        var precipMatches = [
                            text.match(/(\\d+[。.]\\d+)\\s*mm/),
                            text.match(/(\\d+)\\s*mm/)
                        ];
                        for (var j = 0; j < precipMatches.length; j++) {
                            var precipMatch = precipMatches[j];
                            if (precipMatch) {
                                var precipText = precipMatch[1];
                                var precipValue = parseFloat(precipText.replace('。', '.'));
                                if (precipValue >= 0 && precipValue <= 50) {
                                    return {precipitation: precipValue + ' mm'};
                                }
                            }
                        }
                    }
                }
                return {precipitation: '0 mm'};
            """)
        except:
            return {'precipitation': '0 mm'}

    def extract_cloudcover_data(self):
        """提取云量数据"""
        try:
            return self.driver.execute_script("""
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    if (element.offsetParent !== null) {
                        var text = element.textContent || element.innerText || '';
                        var cloudMatch = text.match(/(\\d+)\\s*%/);
                        if (cloudMatch) {
                            var cloudValue = parseInt(cloudMatch[1]);
                            if (cloudValue >= 0 && cloudValue <= 100) {
                                return {cloudcover: cloudValue + '%'};
                            }
                        }
                    }
                }
                return {cloudcover: '0%'};
            """)
        except:
            return {'cloudcover': '0%'}

    def extract_windspeed_data(self):
        """提取风速数据"""
        try:
            return self.driver.execute_script("""
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    if (element.offsetParent !== null) {
                        var text = element.textContent || element.innerText || '';
                        var windMatch = text.match(/(\\d+(?:\\.\\d+)?)\\s*km\\/h/i);
                        if (windMatch) {
                            var windValue = parseFloat(windMatch[1]);
                            if (windValue >= 0 && windValue <= 200) {
                                return {windspeed: windValue + ' km/h'};
                            }
                        }
                    }
                }
                return {windspeed: '0 km/h'};
            """)
        except:
            return {'windspeed': '0 km/h'}

    def switch_to_data_type(self, data_type):
        """切换到指定的数据类型"""
        print(f"🔄 切换到{data_type}图层...")
        
        if data_type == "温度":
            return True  # 已经在温度图层
        
        type_mapping = {
            "降水量": ["降水量", "precipitation"],
            "云量": ["云量", "cloud"],
            "风速": ["风速", "wind"]
        }
        
        keywords = type_mapping.get(data_type, [data_type])
        
        for keyword in keywords:
            try:
                elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        element.click()
                        time.sleep(3)
                        
                        if data_type == "风速":
                            self.select_100m_height()
                        
                        print(f"✅ 成功切换到{data_type}图层")
                        return True
            except:
                continue
        
        print(f"⚠️ 未找到{data_type}选项")
        return False

    def select_100m_height(self):
        """选择100米高度"""
        try:
            height_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '地上10米')]")
            for element in height_elements:
                if element.is_displayed():
                    actions = ActionChains(self.driver)
                    actions.move_to_element(element).click().perform()
                    time.sleep(2)
                    break
            
            option_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '地上100米')]")
            for element in option_elements:
                if element.is_displayed():
                    element.click()
                    time.sleep(2)
                    print("✅ 成功选择地上100米")
                    return True
        except:
            pass
        return False

    def analyze_weather_condition(self, temp, precip, cloud, wind):
        """分析综合天气状况"""
        try:
            temp_val = int(re.search(r'(\d+)', temp).group(1))
            precip_val = float(re.search(r'(\d+(?:\.\d+)?)', precip).group(1))
            cloud_val = int(re.search(r'(\d+)', cloud).group(1))
            wind_val = int(re.search(r'(\d+)', wind).group(1))
            
            if temp_val <= 20:
                temp_desc = "凉爽"
            elif temp_val <= 25:
                temp_desc = "舒适"
            elif temp_val <= 30:
                temp_desc = "温暖"
            else:
                temp_desc = "炎热"
            
            if cloud_val <= 25:
                cloud_desc = "晴朗"
            elif cloud_val <= 50:
                cloud_desc = "少云"
            elif cloud_val <= 75:
                cloud_desc = "多云"
            else:
                cloud_desc = "阴天"
            
            if precip_val == 0:
                precip_desc = ""
                weather_icon = "☀️" if cloud_val <= 50 else "☁️"
            elif precip_val < 2.5:
                precip_desc = "小雨"
                weather_icon = "🌦️"
            elif precip_val < 10:
                precip_desc = "中雨"
                weather_icon = "🌧️"
            else:
                precip_desc = "大雨"
                weather_icon = "⛈️"
            
            if wind_val <= 15:
                wind_desc = "微风"
            elif wind_val <= 25:
                wind_desc = "和风"
            else:
                wind_desc = "强风"
            
            if precip_desc:
                condition = f"{temp_desc}{cloud_desc}{precip_desc}{wind_desc}"
            else:
                condition = f"{temp_desc}{cloud_desc}{wind_desc}"
            
            return f"{weather_icon} {condition}"
        except:
            return "数据解析失败"

    def get_single_day_forecast(self, date):
        """获取单日完整天气预报（修正版：确保图层顺序正确）"""
        print(f"\n📅 获取{date}天气数据...")

        # 1. 根据是否是第一天选择不同的日期切换方法
        if self.current_date_index == 0:
            # 第一天：点击日期选择器更改日期
            if not self.click_date_selector_to_change_date(date):
                return None
        else:
            # 后续天数：使用箭头键前进一天
            if not self.navigate_to_next_date():
                return None

        # 2. 等待页面完全加载后重置到温度图层
        print("⏳ 等待页面完全加载...")
        time.sleep(5)  # 增加等待时间

        if not self.reset_to_temperature_layer():
            return None

        # 3. 再次等待温度图层完全加载
        print("⏳ 等待温度图层完全加载...")
        time.sleep(3)
        
        # 3. 按正确顺序获取数据
        # 获取温度数据（当前已在温度图层）
        print(f"  🌡️ 温度...", end="")
        temp_data = self.get_data_for_all_hours("温度")
        print(f" ✅ {len(temp_data)}/6")
        
        # 切换到降水量并获取数据
        print(f"  🌧️ 降水量...", end="")
        self.switch_to_data_type("降水量")
        precip_data = self.get_data_for_all_hours("降水量")
        print(f" ✅ {len(precip_data)}/6")
        
        # 切换到云量并获取数据
        print(f"  ☁️ 云量...", end="")
        self.switch_to_data_type("云量")
        cloud_data = self.get_data_for_all_hours("云量")
        print(f" ✅ {len(cloud_data)}/6")
        
        # 切换到风速并获取数据
        print(f"  💨 风速...", end="")
        self.switch_to_data_type("风速")
        wind_data = self.get_data_for_all_hours("风速")
        print(f" ✅ {len(wind_data)}/6")
        
        # 4. 整合单日数据
        day_data = {}
        for hour in self.target_hours:
            time_key = f"{hour:02d}:00"
            
            temp = temp_data.get(time_key, {}).get('temperature', 'N/A')
            precip = precip_data.get(time_key, {}).get('precipitation', 'N/A')
            cloud = cloud_data.get(time_key, {}).get('cloudcover', 'N/A')
            wind = wind_data.get(time_key, {}).get('windspeed', 'N/A')
            
            if all(x != 'N/A' for x in [temp, precip, cloud, wind]):
                weather_condition = self.analyze_weather_condition(temp, precip, cloud, wind)
            else:
                weather_condition = "数据不完整"
            
            day_data[time_key] = {
                'temperature': temp,
                'precipitation': precip,
                'cloudcover': cloud,
                'windspeed_100m': wind,
                'weather_condition': weather_condition
            }
        
        return day_data

    def save_partial_data(self, current_day_index):
        """实时保存部分数据（防止程序崩溃丢失数据）"""
        try:
            partial_forecast = {
                'location': {
                    'coordinates': self.format_coordinates(),
                    'lat': self.target_lat,
                    'lon': self.target_lon
                },
                'date_range': {
                    'start': self.start_date,
                    'end': self.end_date,
                    'total_days': len(self.date_list),
                    'completed_days': current_day_index + 1
                },
                'forecast_hours': self.target_hours,
                'weather_data': self.all_weather_data,
                'extraction_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'data_source': 'Ventusky官方 - 实时保存数据',
                'status': 'partial_complete'
            }

            # 保存部分数据
            partial_filename = f"ventusky_partial_{self.start_date.replace('-', '')}_to_{self.end_date.replace('-', '')}_day{current_day_index + 1}.json"
            with open(partial_filename, 'w', encoding='utf-8') as f:
                json.dump(partial_forecast, f, ensure_ascii=False, indent=2)

            print(f"  💾 实时保存: {partial_filename}")

        except Exception as e:
            print(f"  ⚠️ 实时保存失败: {e}")

    def run_multi_day_forecast(self):
        """运行多日天气预报获取（修正版）"""
        try:
            print("🌤️ 开始修正版多日天气预报获取")
            print(f"📍 位置: {self.format_coordinates()}")
            print(f"📅 日期范围: {self.start_date} 到 {self.end_date} (共{len(self.date_list)}天)")
            print(f"🕐 时间点: {', '.join([f'{h:02d}:00' for h in self.target_hours])}")
            print("🔧 修正: 每次切换日期后重新设置图层顺序")
            print("=" * 80)
            
            # 设置页面
            self.setup_page()
            
            # 获取每一天的数据
            for i, date in enumerate(self.date_list):
                try:
                    print(f"\n📊 进度: {i+1}/{len(self.date_list)} - {date}")

                    self.current_date_index = i  # 更新当前日期索引

                    day_data = self.get_single_day_forecast(date)
                    if day_data:
                        self.all_weather_data[date] = day_data
                        print(f"  ✅ {date} 数据获取成功")

                        # 实时保存已获取的数据（防止程序崩溃丢失数据）
                        self.save_partial_data(i)
                    else:
                        print(f"  ❌ {date} 数据获取失败")

                    # 短暂休息
                    time.sleep(2)

                except Exception as e:
                    print(f"❌ 获取 {date} 数据时发生错误: {e}")
                    print(f"📊 已成功获取 {len(self.all_weather_data)} 天数据")

                    # 保存已获取的数据
                    if self.all_weather_data:
                        self.save_partial_data(i)
                        print("💾 已保存部分数据，可以查看已获取的结果")

                    # 继续尝试下一天
                    continue
            
            # 整合所有数据
            complete_forecast = {
                'location': {
                    'coordinates': self.format_coordinates(),
                    'lat': self.target_lat,
                    'lon': self.target_lon
                },
                'date_range': {
                    'start': self.start_date,
                    'end': self.end_date,
                    'total_days': len(self.date_list)
                },
                'forecast_hours': self.target_hours,
                'weather_data': self.all_weather_data,
                'extraction_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'data_source': 'Ventusky官方 - 修正版多日天气预报系统'
            }
            
            # 保存和导出数据
            self.save_multi_day_forecast(complete_forecast)
            self.export_multi_day_to_excel(complete_forecast)
            self.display_multi_day_summary(complete_forecast)
            
            return complete_forecast
            
        finally:
            if self.driver:
                print("\n⏳ 保持浏览器打开30秒...")
                time.sleep(30)
                self.driver.quit()

    def save_multi_day_forecast(self, forecast_data):
        """保存多日预报数据"""
        filename = f"ventusky_multi_day_forecast_{self.start_date.replace('-', '')}_to_{self.end_date.replace('-', '')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(forecast_data, f, ensure_ascii=False, indent=2)
        print(f"\n💾 多日天气预报已保存到: {filename}")

    def export_multi_day_to_excel(self, forecast_data):
        """导出多日数据到Excel（包含改进的格式）"""
        print("\n📊 开始导出多日Excel文件...")

        try:
            # 创建Excel工作簿
            wb = Workbook()

            # 第一个工作表：标准格式
            ws1 = wb.active
            ws1.title = "标准格式"
            self._create_standard_excel_sheet(ws1, forecast_data)

            # 第二个工作表：改进格式（按工作簿1格式）
            ws2 = wb.create_sheet("改进格式")
            self._create_improved_excel_sheet(ws2, forecast_data)

            # 第三个工作表：原始数据
            ws3 = wb.create_sheet("原始数据")
            self._create_raw_data_sheet(ws3, forecast_data)

            # 保存Excel文件
            excel_filename = f"ventusky_multi_day_forecast_{self.start_date.replace('-', '')}_to_{self.end_date.replace('-', '')}.xlsx"
            wb.save(excel_filename)

            print(f"✅ 多日Excel文件已保存到: {excel_filename}")
            print(f"📋 包含3个工作表: 标准格式、改进格式、原始数据")

        except Exception as e:
            print(f"❌ 多日Excel导出失败: {e}")

    def _create_standard_excel_sheet(self, ws, forecast_data):
        """创建标准格式的Excel工作表"""
        # 设置标题
        title = f"{forecast_data['date_range']['start']} 到 {forecast_data['date_range']['end']} 天气预报 ({forecast_data['location']['coordinates']})"
        ws['A1'] = title
        ws.merge_cells('A1:G1')

        # 标题样式
        title_font = Font(name='微软雅黑', size=14, bold=True, color='FFFFFF')
        title_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
        title_alignment = Alignment(horizontal='center', vertical='center')
        ws['A1'].font = title_font
        ws['A1'].fill = title_fill
        ws['A1'].alignment = title_alignment
        ws.row_dimensions[1].height = 30

        # 设置表头
        headers = ['日期', '时间', '温度', '降水量', '云量', '风速(100m)', '综合天气']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=2, column=col, value=header)
            cell.font = Font(name='微软雅黑', size=11, bold=True, color='FFFFFF')
            cell.fill = PatternFill(start_color='5B9BD5', end_color='5B9BD5', fill_type='solid')
            cell.alignment = Alignment(horizontal='center', vertical='center')

        ws.row_dimensions[2].height = 25

        # 填充数据
        row = 3
        for date in self.date_list:
            if date in forecast_data['weather_data']:
                day_data = forecast_data['weather_data'][date]

                for hour in self.target_hours:
                    time_key = f"{hour:02d}:00"
                    if time_key in day_data:
                        data = day_data[time_key]

                        row_data = [
                            date,
                            time_key,
                            data.get('temperature', 'N/A'),
                            data.get('precipitation', 'N/A'),
                            data.get('cloudcover', 'N/A'),
                            data.get('windspeed_100m', 'N/A'),
                            data.get('weather_condition', 'N/A').replace('🌦️ ', '').replace('🌧️ ', '').replace('☀️ ', '').replace('☁️ ', '').replace('⛈️ ', '')
                        ]

                        for col, value in enumerate(row_data, 1):
                            cell = ws.cell(row=row, column=col, value=value)
                            cell.font = Font(name='微软雅黑', size=10)
                            cell.alignment = Alignment(horizontal='center', vertical='center')

                            # 交替行颜色
                            if row % 2 == 0:
                                cell.fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')

                        ws.row_dimensions[row].height = 20
                        row += 1

        # 设置列宽
        column_widths = [12, 8, 8, 10, 8, 12, 20]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + col)].width = width

    def _create_improved_excel_sheet(self, ws, forecast_data):
        """创建改进格式的Excel工作表（完全按照工作簿1.xlsx格式）"""
        from datetime import datetime, timedelta

        # 完全按照工作簿1.xlsx的表头设置
        headers = ['日期', '日期类型', '无', '查看日', '天气过程描述', '气温', '无', '降水', '云量', '风速', '无', '无']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(name='宋体', size=11, bold=False)
            cell.alignment = Alignment(horizontal='center', vertical='center')

        # 设置列宽（按照工作簿1.xlsx）
        ws.column_dimensions['A'].width = 10.92  # 日期列
        ws.column_dimensions['B'].width = 10     # 日期类型
        ws.column_dimensions['C'].width = 8      # 无
        ws.column_dimensions['D'].width = 10     # 查看日
        ws.column_dimensions['E'].width = 15     # 天气过程描述
        ws.column_dimensions['F'].width = 25     # 气温
        ws.column_dimensions['G'].width = 8      # 无
        ws.column_dimensions['H'].width = 25     # 降水
        ws.column_dimensions['I'].width = 25     # 云量
        ws.column_dimensions['J'].width = 25     # 风速
        ws.column_dimensions['K'].width = 8      # 无
        ws.column_dimensions['L'].width = 8      # 无

        # 查看日期（使用Excel日期序列号格式，45868对应某个日期）
        view_date = datetime.strptime(self.start_date, "%Y-%m-%d")
        view_date_serial = (view_date - datetime(1900, 1, 1)).days + 2  # Excel日期序列号

        # 填充数据
        row = 2
        for date_str in self.date_list:
            if date_str in forecast_data['weather_data']:
                day_data = forecast_data['weather_data'][date_str]

                # 解析日期
                date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                weekday = self._get_weekday_chinese(date_obj)

                # 合并当天所有时间点的数据
                temp_parts = []
                precip_parts = []
                cloud_parts = []
                wind_parts = []

                for hour in self.target_hours:
                    time_key = f"{hour:02d}:00"
                    if time_key in day_data:
                        data = day_data[time_key]

                        # 提取数值（去掉单位）
                        temp = data.get('temperature', 'N/A').replace('°C', '')
                        precip = data.get('precipitation', 'N/A').replace(' mm', '')
                        cloud = data.get('cloudcover', 'N/A').replace('%', '')
                        wind = data.get('windspeed_100m', 'N/A').replace(' km/h', '')

                        temp_parts.append(f"{hour:02d}点{temp}℃")
                        precip_parts.append(f"{hour:02d}点{precip}mm")
                        cloud_parts.append(f"{hour:02d}点{cloud}%")
                        wind_parts.append(f"{hour:02d}点{wind}km/h")

                # 按照工作簿1.xlsx的格式，用空格分隔，末尾加换行
                temp_text = "  ".join(temp_parts) + "\n\n"
                precip_text = "    ".join(precip_parts) + "\n\n"
                cloud_text = " ".join(cloud_parts) + "\n\n"
                wind_text = " ".join(wind_parts) + "\n\n"

                # 填充行数据（按照工作簿1.xlsx的列顺序）
                ws.cell(row=row, column=1, value=date_obj)  # A列：日期（datetime格式）
                ws.cell(row=row, column=2, value=weekday)   # B列：星期
                ws.cell(row=row, column=3, value="")        # C列：无
                ws.cell(row=row, column=4, value=view_date_serial)  # D列：查看日（序列号）
                ws.cell(row=row, column=5, value="")        # E列：天气过程描述
                ws.cell(row=row, column=6, value=temp_text) # F列：气温
                ws.cell(row=row, column=7, value="")        # G列：无
                ws.cell(row=row, column=8, value=precip_text) # H列：降水
                ws.cell(row=row, column=9, value=cloud_text)  # I列：云量
                ws.cell(row=row, column=10, value=wind_text)  # J列：风速
                ws.cell(row=row, column=11, value="")         # K列：无
                ws.cell(row=row, column=12, value="")         # L列：无

                # 设置所有单元格的格式（按照工作簿1.xlsx）
                for col in range(1, 13):
                    cell = ws.cell(row=row, column=col)
                    cell.font = Font(name='宋体', size=11, bold=False)
                    cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

                # 设置行高（按照工作簿1.xlsx的252.0）
                ws.row_dimensions[row].height = 252.0

                row += 1

    def _create_raw_data_sheet(self, ws, forecast_data):
        """创建原始数据工作表"""
        import json

        # 设置标题
        ws['A1'] = "原始JSON数据"
        ws['A1'].font = Font(name='微软雅黑', size=14, bold=True)

        # 将JSON数据转换为可读格式
        json_str = json.dumps(forecast_data, ensure_ascii=False, indent=2)

        # 分行显示JSON数据
        lines = json_str.split('\n')
        for i, line in enumerate(lines, 2):
            ws[f'A{i}'] = line
            ws[f'A{i}'].font = Font(name='Consolas', size=9)

        # 设置列宽
        ws.column_dimensions['A'].width = 80

    def _get_weekday_chinese(self, date):
        """获取中文星期"""
        weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
        return weekdays[date.weekday()]

    def display_multi_day_summary(self, forecast_data):
        """显示多日天气预报摘要"""
        print("\n" + "=" * 80)
        print(f"🌤️ 修正版多日天气预报摘要 ({forecast_data['location']['coordinates']})")
        print(f"📅 {forecast_data['date_range']['start']} 到 {forecast_data['date_range']['end']} (共{forecast_data['date_range']['total_days']}天)")
        print("=" * 80)
        
        successful_days = len(forecast_data['weather_data'])
        print(f"📊 数据获取成功: {successful_days}/{forecast_data['date_range']['total_days']} 天")
        
        if successful_days > 0:
            print(f"\n✅ 成功获取的日期:")
            for date in sorted(forecast_data['weather_data'].keys()):
                day_data = forecast_data['weather_data'][date]
                data_count = len([h for h in day_data.values() if h.get('temperature') != 'N/A'])
                print(f"  {date}: {data_count}/6 个时间点")
        
        print(f"\n📁 输出文件:")
        print(f"  • JSON: ventusky_multi_day_forecast_{self.start_date.replace('-', '')}_to_{self.end_date.replace('-', '')}.json")
        print(f"  • Excel: ventusky_multi_day_forecast_{self.start_date.replace('-', '')}_to_{self.end_date.replace('-', '')}.xlsx")

if __name__ == "__main__":
    # 检查依赖包
    try:
        import pandas as pd
        import openpyxl
        print("✓ Excel导出依赖包检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("请安装: pip install pandas openpyxl")
        exit(1)
    
    # 创建修正版多日天气预报获取器（指定位置8月5日）
    forecaster = VentuskyMultiDayForecastFixed(
        lat=30.190,   # 30.190°
        lon=120.203,  # 120.203°
        start_date="2025-08-05",  # 2025年8月5日
        end_date="2025-08-05"     # 单日提取
    )
    
    result = forecaster.run_multi_day_forecast()
    
    if result:
        print("\n🎉 修正版多日天气预报获取成功！")
        print("📊 已生成JSON和Excel格式的完整数据文件")
        print("🔧 修正了图层切换顺序问题")
    else:
        print("\n❌ 修正版多日天气预报获取失败")
