#!/usr/bin/env python3
"""
Ventusky Weather System - 主启动脚本
"""

import sys
import os
from datetime import datetime, timedelta

def show_menu():
    """显示主菜单"""
    print("=" * 60)
    print("🌤️  Ventusky Weather System")
    print("=" * 60)
    print("1. 单日完整天气预报 (4参数 × 6时间点)")
    print("2. 多日天气预报 (批量获取)")
    print("3. 查看历史数据")
    print("4. 系统设置")
    print("0. 退出")
    print("=" * 60)

def single_day_forecast():
    """单日天气预报"""
    print("\n🌡️ 单日完整天气预报")
    print("-" * 40)
    
    # 获取用户输入
    try:
        lat = float(input("请输入纬度 (默认: 30.1833): ") or "30.1833")
        lon = float(input("请输入经度 (默认: 120.2): ") or "120.2")
        date = input("请输入日期 (YYYY-MM-DD, 默认: 今天): ") or datetime.now().strftime("%Y-%m-%d")
        
        print(f"\n📍 位置: {lat}°, {lon}°")
        print(f"📅 日期: {date}")
        print("🔄 开始获取天气数据...")
        
        # 导入并运行单日预报
        from ventusky_complete_weather_excel import VentuskyCompleteWeatherExcel
        
        forecaster = VentuskyCompleteWeatherExcel(lat=lat, lon=lon, date=date)
        result = forecaster.run_complete_forecast()
        
        if result:
            print("\n✅ 单日天气预报获取成功！")
        else:
            print("\n❌ 单日天气预报获取失败")
            
    except ValueError:
        print("❌ 输入格式错误，请检查坐标和日期格式")
    except Exception as e:
        print(f"❌ 获取失败: {e}")

def multi_day_forecast():
    """多日天气预报"""
    print("\n📊 多日天气预报")
    print("-" * 40)
    
    try:
        lat = float(input("请输入纬度 (默认: 30.1833): ") or "30.1833")
        lon = float(input("请输入经度 (默认: 120.2): ") or "120.2")
        
        # 默认日期范围
        today = datetime.now()
        default_start = today.strftime("%Y-%m-%d")
        default_end = (today + timedelta(days=7)).strftime("%Y-%m-%d")
        
        start_date = input(f"请输入开始日期 (默认: {default_start}): ") or default_start
        end_date = input(f"请输入结束日期 (默认: {default_end}): ") or default_end
        
        print(f"\n📍 位置: {lat}°, {lon}°")
        print(f"📅 日期范围: {start_date} 到 {end_date}")
        print("🔄 开始获取多日天气数据...")
        
        # 导入并运行多日预报
        from ventusky_multi_day_forecast_fixed import VentuskyMultiDayForecastFixed
        
        forecaster = VentuskyMultiDayForecastFixed(
            lat=lat, 
            lon=lon, 
            start_date=start_date, 
            end_date=end_date
        )
        result = forecaster.run_multi_day_forecast()
        
        if result:
            print("\n✅ 多日天气预报获取成功！")
        else:
            print("\n❌ 多日天气预报获取失败")
            
    except ValueError:
        print("❌ 输入格式错误，请检查坐标和日期格式")
    except Exception as e:
        print(f"❌ 获取失败: {e}")

def view_history():
    """查看历史数据"""
    print("\n📁 历史数据文件")
    print("-" * 40)
    
    output_dir = "output"
    if os.path.exists(output_dir):
        files = [f for f in os.listdir(output_dir) if f.endswith(('.json', '.xlsx', '.csv'))]
        
        if files:
            print("📊 可用的数据文件:")
            for i, file in enumerate(files, 1):
                file_path = os.path.join(output_dir, file)
                size = os.path.getsize(file_path)
                print(f"  {i}. {file} ({size} bytes)")
        else:
            print("📭 暂无历史数据文件")
    else:
        print("📭 output目录不存在")

def system_settings():
    """系统设置"""
    print("\n⚙️ 系统设置")
    print("-" * 40)
    print("📦 依赖包状态:")
    
    # 检查依赖包
    required_packages = [
        'selenium', 'webdriver_manager', 'pandas', 'openpyxl'
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (未安装)")
    
    print(f"\n📁 项目目录: {os.getcwd()}")
    print(f"🐍 Python版本: {sys.version}")

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择功能 (0-4): ").strip()
            
            if choice == "0":
                print("\n👋 感谢使用 Ventusky Weather System!")
                break
            elif choice == "1":
                single_day_forecast()
            elif choice == "2":
                multi_day_forecast()
            elif choice == "3":
                view_history()
            elif choice == "4":
                system_settings()
            else:
                print("❌ 无效选择，请输入 0-4")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"\n❌ 程序错误: {e}")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
