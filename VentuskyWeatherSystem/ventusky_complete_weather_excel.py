#!/usr/bin/env python3
"""
完整天气预报获取系统 + Excel导出功能
"""

import json
import time
import re
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

# Excel相关导入
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

class VentuskyCompleteWeatherExcel:
    def __init__(self, lat=30.1833, lon=120.2, date="2025-07-26"):
        self.driver = None
        self.target_lat = lat
        self.target_lon = lon
        self.target_date = date
        self.target_hours = [2, 5, 8, 11, 17, 23]
        self.weather_data = {}
        self.setup_driver()
        
    def setup_driver(self):
        """设置Chrome驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            print("✓ Chrome驱动设置成功")
        except Exception as e:
            print(f"❌ Chrome驱动设置失败: {e}")
            raise

    def setup_page(self):
        """设置页面"""
        print(f"🎯 导航到位置: {self.format_coordinates()}")
        print(f"📅 目标日期: {self.target_date}")
        
        target_url = f"https://www.ventusky.com/?p={self.target_lat};{self.target_lon};11&l=temperature-2m"
        self.driver.get(target_url)
        
        WebDriverWait(self.driver, 20).until(
            EC.presence_of_element_located((By.TAG_NAME, "canvas"))
        )
        time.sleep(10)
        print("✓ 页面加载完成")

    def format_coordinates(self):
        """格式化坐标显示"""
        lat_deg = int(self.target_lat)
        lat_min = int((self.target_lat - lat_deg) * 60)
        lon_deg = int(self.target_lon)
        lon_min = int((self.target_lon - lon_deg) * 60)
        return f"{lat_deg}°{lat_min}' {lon_deg}°{lon_min}'"

    def navigate_to_target_date(self):
        """导航到目标日期"""
        print(f"📅 导航到{self.target_date}...")
        
        try:
            from selenium.webdriver.common.keys import Keys
            self.driver.find_element(By.TAG_NAME, "body").click()
            time.sleep(1)
            
            actions = ActionChains(self.driver)
            actions.send_keys(Keys.ARROW_RIGHT).perform()
            time.sleep(2)
            actions.send_keys(Keys.PAGE_DOWN).perform()
            time.sleep(2)
            actions.send_keys('+').perform()
            time.sleep(2)
            
            print("✅ 日期导航完成")
            return True
        except Exception as e:
            print(f"❌ 日期导航失败: {e}")
            return False

    def find_timeline_elements(self):
        """找到时间轴元素"""
        timeline_elements = []
        all_links = self.driver.find_elements(By.TAG_NAME, "a")
        
        for link in all_links:
            try:
                if link.is_displayed():
                    location = link.location
                    size = link.size
                    
                    if (location['y'] > 800 and  
                        size['width'] > 30 and size['width'] < 80 and  
                        size['height'] > 20 and size['height'] < 50):
                        
                        timeline_elements.append({
                            'element': link,
                            'x_position': location['x']
                        })
            except:
                continue
        
        timeline_elements.sort(key=lambda x: x['x_position'])
        return timeline_elements

    def get_data_for_all_hours(self, data_type):
        """获取所有时间点的数据"""
        print(f"📊 获取{data_type}数据...")
        
        timeline_elements = self.find_timeline_elements()
        if not timeline_elements:
            print("❌ 未找到时间轴元素")
            return {}
        
        total_elements = len(timeline_elements)
        data_results = {}
        
        for target_hour in self.target_hours:
            try:
                print(f"  🕐 {target_hour:02d}:00...", end="")
                
                # 点击时间轴
                hours_per_element = 24 / total_elements
                element_index = int(target_hour / hours_per_element)
                element_index = max(0, min(element_index, total_elements - 1))
                
                element = timeline_elements[element_index]['element']
                actions = ActionChains(self.driver)
                actions.move_to_element(element).click().perform()
                time.sleep(3)
                
                # 悬停获取数据
                canvas = self.driver.find_element(By.TAG_NAME, "canvas")
                canvas_size = canvas.size
                center_x = canvas_size['width'] // 2
                center_y = canvas_size['height'] // 2
                
                actions = ActionChains(self.driver)
                actions.move_to_element_with_offset(canvas, center_x, center_y).perform()
                time.sleep(3)
                
                # 提取数据
                if data_type == "温度":
                    hour_data = self.extract_temperature_data()
                elif data_type == "降水量":
                    hour_data = self.extract_precipitation_data()
                elif data_type == "云量":
                    hour_data = self.extract_cloudcover_data()
                elif data_type == "风速":
                    hour_data = self.extract_windspeed_data()
                
                if hour_data:
                    data_results[f"{target_hour:02d}:00"] = hour_data
                    print(" ✅")
                else:
                    print(" ❌")
                
                time.sleep(1)
                
            except Exception as e:
                print(f" ❌ {e}")
                continue
        
        return data_results

    def extract_temperature_data(self):
        """提取温度数据"""
        try:
            return self.driver.execute_script("""
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    if (element.offsetParent !== null) {
                        var text = element.textContent || element.innerText || '';
                        var tempMatch = text.match(/(-?\\d+)\\s*°C?/);
                        if (tempMatch) {
                            var tempValue = parseInt(tempMatch[1]);
                            if (tempValue >= -50 && tempValue <= 60) {
                                return {temperature: tempValue + '°C'};
                            }
                        }
                    }
                }
                return null;
            """)
        except:
            return None

    def extract_precipitation_data(self):
        """提取降水量数据（支持中文句号）"""
        try:
            return self.driver.execute_script("""
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    if (element.offsetParent !== null) {
                        var text = element.textContent || element.innerText || '';
                        var precipMatches = [
                            text.match(/(\\d+[。.]\\d+)\\s*mm/),
                            text.match(/(\\d+)\\s*mm/)
                        ];
                        for (var j = 0; j < precipMatches.length; j++) {
                            var precipMatch = precipMatches[j];
                            if (precipMatch) {
                                var precipText = precipMatch[1];
                                var precipValue = parseFloat(precipText.replace('。', '.'));
                                if (precipValue >= 0 && precipValue <= 50) {
                                    return {precipitation: precipValue + ' mm'};
                                }
                            }
                        }
                    }
                }
                return {precipitation: '0 mm'};
            """)
        except:
            return {'precipitation': '0 mm'}

    def extract_cloudcover_data(self):
        """提取云量数据"""
        try:
            return self.driver.execute_script("""
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    if (element.offsetParent !== null) {
                        var text = element.textContent || element.innerText || '';
                        var cloudMatch = text.match(/(\\d+)\\s*%/);
                        if (cloudMatch) {
                            var cloudValue = parseInt(cloudMatch[1]);
                            if (cloudValue >= 0 && cloudValue <= 100) {
                                return {cloudcover: cloudValue + '%'};
                            }
                        }
                    }
                }
                return {cloudcover: '0%'};
            """)
        except:
            return {'cloudcover': '0%'}

    def extract_windspeed_data(self):
        """提取风速数据"""
        try:
            return self.driver.execute_script("""
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    if (element.offsetParent !== null) {
                        var text = element.textContent || element.innerText || '';
                        var windMatch = text.match(/(\\d+(?:\\.\\d+)?)\\s*km\\/h/i);
                        if (windMatch) {
                            var windValue = parseFloat(windMatch[1]);
                            if (windValue >= 0 && windValue <= 200) {
                                return {windspeed: windValue + ' km/h'};
                            }
                        }
                    }
                }
                return {windspeed: '0 km/h'};
            """)
        except:
            return {'windspeed': '0 km/h'}

    def switch_to_data_type(self, data_type):
        """切换到指定的数据类型"""
        print(f"🔄 切换到{data_type}图层...")
        
        if data_type == "温度":
            return True
        
        type_mapping = {
            "降水量": ["降水量", "precipitation"],
            "云量": ["云量", "cloud"],
            "风速": ["风速", "wind"]
        }
        
        keywords = type_mapping.get(data_type, [data_type])
        
        for keyword in keywords:
            try:
                elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        element.click()
                        time.sleep(5)
                        
                        if data_type == "风速":
                            self.select_100m_height()
                        
                        print(f"✅ 成功切换到{data_type}图层")
                        return True
            except:
                continue
        
        print(f"⚠️ 未找到{data_type}选项，使用当前图层")
        return False

    def select_100m_height(self):
        """选择100米高度"""
        try:
            height_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '地上10米')]")
            for element in height_elements:
                if element.is_displayed():
                    actions = ActionChains(self.driver)
                    actions.move_to_element(element).click().perform()
                    time.sleep(3)
                    break
            
            option_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '地上100米')]")
            for element in option_elements:
                if element.is_displayed():
                    element.click()
                    time.sleep(3)
                    print("✅ 成功选择地上100米")
                    return True
        except:
            pass
        return False

    def analyze_weather_condition(self, temp, precip, cloud, wind):
        """分析综合天气状况"""
        temp_val = int(re.search(r'(\d+)', temp).group(1))
        precip_val = float(re.search(r'(\d+(?:\.\d+)?)', precip).group(1))
        cloud_val = int(re.search(r'(\d+)', cloud).group(1))
        wind_val = int(re.search(r'(\d+)', wind).group(1))
        
        if temp_val <= 20:
            temp_desc = "凉爽"
        elif temp_val <= 25:
            temp_desc = "舒适"
        elif temp_val <= 30:
            temp_desc = "温暖"
        else:
            temp_desc = "炎热"
        
        if cloud_val <= 25:
            cloud_desc = "晴朗"
        elif cloud_val <= 50:
            cloud_desc = "少云"
        elif cloud_val <= 75:
            cloud_desc = "多云"
        else:
            cloud_desc = "阴天"
        
        if precip_val == 0:
            precip_desc = ""
            weather_icon = "☀️" if cloud_val <= 50 else "☁️"
        elif precip_val < 2.5:
            precip_desc = "小雨"
            weather_icon = "🌦️"
        elif precip_val < 10:
            precip_desc = "中雨"
            weather_icon = "🌧️"
        else:
            precip_desc = "大雨"
            weather_icon = "⛈️"
        
        if wind_val <= 15:
            wind_desc = "微风"
        elif wind_val <= 25:
            wind_desc = "和风"
        else:
            wind_desc = "强风"
        
        if precip_desc:
            condition = f"{temp_desc}{cloud_desc}{precip_desc}{wind_desc}"
        else:
            condition = f"{temp_desc}{cloud_desc}{wind_desc}"
        
        return f"{weather_icon} {condition}"

    def run_complete_forecast(self):
        """运行完整天气预报获取"""
        try:
            print("🌤️ 开始完整天气预报获取")
            print(f"📍 位置: {self.format_coordinates()}")
            print(f"📅 日期: {self.target_date}")
            print(f"🕐 时间点: {', '.join([f'{h:02d}:00' for h in self.target_hours])}")
            print("=" * 80)
            
            # 1. 设置页面
            self.setup_page()
            
            # 2. 导航到目标日期
            self.navigate_to_target_date()
            
            # 3. 获取温度数据（默认图层）
            print("\n🌡️ 获取温度数据...")
            temp_data = self.get_data_for_all_hours("温度")
            
            # 4. 切换到降水量并获取数据
            print("\n🌧️ 获取降水量数据...")
            self.switch_to_data_type("降水量")
            precip_data = self.get_data_for_all_hours("降水量")
            
            # 5. 切换到云量并获取数据
            print("\n☁️ 获取云量数据...")
            self.switch_to_data_type("云量")
            cloud_data = self.get_data_for_all_hours("云量")
            
            # 6. 切换到风速并获取数据
            print("\n💨 获取风速数据...")
            self.switch_to_data_type("风速")
            wind_data = self.get_data_for_all_hours("风速")
            
            # 7. 整合所有数据
            complete_forecast = self.integrate_all_data(temp_data, precip_data, cloud_data, wind_data)
            
            # 8. 保存JSON结果
            self.save_complete_forecast(complete_forecast)
            
            # 9. 导出Excel文件
            self.export_to_excel(complete_forecast)
            
            # 10. 显示结果
            self.display_complete_forecast(complete_forecast)
            
            return complete_forecast
            
        finally:
            if self.driver:
                print("\n⏳ 保持浏览器打开60秒...")
                time.sleep(60)
                self.driver.quit()

    def integrate_all_data(self, temp_data, precip_data, cloud_data, wind_data):
        """整合所有天气数据"""
        integrated_data = {
            'location': {
                'coordinates': self.format_coordinates(),
                'lat': self.target_lat,
                'lon': self.target_lon
            },
            'date': self.target_date,
            'forecast_hours': self.target_hours,
            'weather_data': {},
            'extraction_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'data_source': 'Ventusky官方 - 完整天气预报系统'
        }
        
        for hour in self.target_hours:
            time_key = f"{hour:02d}:00"
            
            temp = temp_data.get(time_key, {}).get('temperature', 'N/A')
            precip = precip_data.get(time_key, {}).get('precipitation', 'N/A')
            cloud = cloud_data.get(time_key, {}).get('cloudcover', 'N/A')
            wind = wind_data.get(time_key, {}).get('windspeed', 'N/A')
            
            if all(x != 'N/A' for x in [temp, precip, cloud, wind]):
                weather_condition = self.analyze_weather_condition(temp, precip, cloud, wind)
            else:
                weather_condition = "数据不完整"
            
            integrated_data['weather_data'][time_key] = {
                'temperature': temp,
                'precipitation': precip,
                'cloudcover': cloud,
                'windspeed_100m': wind,
                'weather_condition': weather_condition
            }
        
        return integrated_data

    def save_complete_forecast(self, forecast_data):
        """保存完整预报数据"""
        filename = f"ventusky_complete_forecast_{self.target_date.replace('-', '')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(forecast_data, f, ensure_ascii=False, indent=2)
        print(f"\n💾 完整天气预报已保存到: {filename}")

    def export_to_excel(self, forecast_data):
        """导出到Excel文件"""
        print("\n📊 开始导出Excel文件...")
        
        try:
            # 创建Excel工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "天气预报"
            
            # 设置标题
            title = f"{forecast_data['date']} {forecast_data['location']['coordinates']} 天气预报"
            ws['A1'] = title
            ws.merge_cells('A1:F1')
            
            # 标题样式
            title_font = Font(name='微软雅黑', size=16, bold=True, color='FFFFFF')
            title_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
            title_alignment = Alignment(horizontal='center', vertical='center')
            ws['A1'].font = title_font
            ws['A1'].fill = title_fill
            ws['A1'].alignment = title_alignment
            ws.row_dimensions[1].height = 30
            
            # 设置表头
            headers = ['时间', '温度', '降水量', '云量', '风速(100m)', '综合天气']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=2, column=col, value=header)
                cell.font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
                cell.fill = PatternFill(start_color='5B9BD5', end_color='5B9BD5', fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')
            
            ws.row_dimensions[2].height = 25
            
            # 填充数据
            for row, hour in enumerate(self.target_hours, 3):
                time_key = f"{hour:02d}:00"
                data = forecast_data['weather_data'].get(time_key, {})
                
                # 数据行
                row_data = [
                    time_key,
                    data.get('temperature', 'N/A'),
                    data.get('precipitation', 'N/A'),
                    data.get('cloudcover', 'N/A'),
                    data.get('windspeed_100m', 'N/A'),
                    data.get('weather_condition', 'N/A').replace('🌦️ ', '').replace('🌧️ ', '').replace('☀️ ', '').replace('☁️ ', '').replace('⛈️ ', '')
                ]
                
                for col, value in enumerate(row_data, 1):
                    cell = ws.cell(row=row, column=col, value=value)
                    cell.font = Font(name='微软雅黑', size=11)
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    
                    # 交替行颜色
                    if row % 2 == 0:
                        cell.fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')
                
                ws.row_dimensions[row].height = 22
            
            # 设置边框
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            for row in range(2, len(self.target_hours) + 3):
                for col in range(1, 7):
                    ws.cell(row=row, column=col).border = thin_border
            
            # 设置列宽
            column_widths = [12, 10, 12, 10, 15, 25]
            for col, width in enumerate(column_widths, 1):
                ws.column_dimensions[chr(64 + col)].width = width
            
            # 添加数据来源信息
            info_row = len(self.target_hours) + 4
            ws[f'A{info_row}'] = f"数据来源: {forecast_data['data_source']}"
            ws[f'A{info_row}'].font = Font(name='微软雅黑', size=10, italic=True)
            
            ws[f'A{info_row + 1}'] = f"获取时间: {forecast_data['extraction_time']}"
            ws[f'A{info_row + 1}'].font = Font(name='微软雅黑', size=10, italic=True)
            
            ws[f'A{info_row + 2}'] = f"坐标位置: {forecast_data['location']['coordinates']} (纬度: {forecast_data['location']['lat']}, 经度: {forecast_data['location']['lon']})"
            ws[f'A{info_row + 2}'].font = Font(name='微软雅黑', size=10, italic=True)
            
            # 保存Excel文件
            excel_filename = f"ventusky_weather_forecast_{self.target_date.replace('-', '')}.xlsx"
            wb.save(excel_filename)
            
            print(f"✅ Excel文件已保存到: {excel_filename}")
            
            # 同时创建简化的CSV文件
            self.export_to_csv(forecast_data)
            
        except Exception as e:
            print(f"❌ Excel导出失败: {e}")
            print("尝试创建简化版CSV文件...")
            self.export_to_csv(forecast_data)

    def export_to_csv(self, forecast_data):
        """导出到CSV文件（备用方案）"""
        try:
            # 准备DataFrame数据
            data_rows = []
            for hour in self.target_hours:
                time_key = f"{hour:02d}:00"
                data = forecast_data['weather_data'].get(time_key, {})
                
                row = {
                    '时间': time_key,
                    '温度': data.get('temperature', 'N/A'),
                    '降水量': data.get('precipitation', 'N/A'),
                    '云量': data.get('cloudcover', 'N/A'),
                    '风速(100m)': data.get('windspeed_100m', 'N/A'),
                    '综合天气': data.get('weather_condition', 'N/A')
                }
                data_rows.append(row)
            
            # 创建DataFrame
            df = pd.DataFrame(data_rows)
            
            # 保存CSV文件
            csv_filename = f"ventusky_weather_forecast_{self.target_date.replace('-', '')}.csv"
            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            
            print(f"✅ CSV文件已保存到: {csv_filename}")
            
        except Exception as e:
            print(f"❌ CSV导出也失败: {e}")

    def display_complete_forecast(self, forecast_data):
        """显示完整天气预报"""
        print("\n" + "=" * 80)
        print(f"🌤️ {forecast_data['date']} {forecast_data['location']['coordinates']} 完整天气预报")
        print("=" * 80)
        
        print(f"{'时间':<8} {'温度':<8} {'降水量':<10} {'云量':<8} {'风速(100m)':<12} {'综合天气'}")
        print("-" * 80)
        
        for hour in self.target_hours:
            time_key = f"{hour:02d}:00"
            data = forecast_data['weather_data'].get(time_key, {})
            
            temp = data.get('temperature', 'N/A')
            precip = data.get('precipitation', 'N/A')
            cloud = data.get('cloudcover', 'N/A')
            wind = data.get('windspeed_100m', 'N/A')
            condition = data.get('weather_condition', 'N/A')
            
            print(f"{time_key:<8} {temp:<8} {precip:<10} {cloud:<8} {wind:<12} {condition}")
        
        print("\n📊 数据来源: " + forecast_data['data_source'])
        print(f"⏰ 获取时间: {forecast_data['extraction_time']}")
        print(f"📁 文件输出: JSON + Excel + CSV 三种格式")

if __name__ == "__main__":
    # 检查依赖包
    try:
        import pandas as pd
        import openpyxl
        print("✓ Excel导出依赖包检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("请安装: pip install pandas openpyxl")
        exit(1)
    
    # 可以自定义坐标和日期
    forecaster = VentuskyCompleteWeatherExcel(
        lat=30.1833,  # 30°11'
        lon=120.2,    # 120°12'
        date="2025-07-26"
    )
    
    result = forecaster.run_complete_forecast()
    
    if result:
        print("\n🎉 完整天气预报获取成功！")
        print("📊 已生成以下文件:")
        print("   • JSON格式: 结构化数据存储")
        print("   • Excel格式: 美观的表格文件")
        print("   • CSV格式: 通用的数据文件")
    else:
        print("\n❌ 完整天气预报获取失败")
